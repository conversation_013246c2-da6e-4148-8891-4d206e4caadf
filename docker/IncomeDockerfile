FROM django-base:latest
ENV DJANGO_SETTINGS_MODULE config.settings.production
ADD ./requirements /requirements
RUN pip install --no-cache-dir --trusted-host mirrors.aliyun.com --index-url http://mirrors.aliyun.com/pypi/simple/ -r /requirements/production.txt
ONBUILD ADD . /app
ONBUILD WORKDIR /app
ONBUILD RUN pip install --no-cache-dir --trusted-host pypi.tuna.tsinghua.edu.cn/simple --index-url http://pypi.tuna.tsinghua.edu.cn/simple -r requirements/production.txt

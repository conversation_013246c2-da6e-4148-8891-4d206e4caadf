FROM python:3.11-slim

ENV TZ=Asia/Shanghai

RUN echo "deb http://mirrors.aliyun.com/debian bookworm main" > /etc/apt/sources.list && \
    echo "deb http://mirrors.aliyun.com/debian bookworm-updates main" >> /etc/apt/sources.list && \
    echo "deb http://mirrors.aliyun.com/debian-security bookworm-security main" >> /etc/apt/sources.list

RUN apt-get update && apt-get install -y --allow-unauthenticated \
                gettext sqlite3 wget unzip python3-dev python3-mysqldb default-libmysqlclient-dev \
                libcurl4-openssl-dev libssl-dev zlib1g-dev curl git supervisor\
                build-essential python3-pip vim pkg-config --no-install-recommends &&  \
                rm -rf /var/lib/apt/lists/*
CMD ["/app/script/run.sh"]

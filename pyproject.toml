# ==== pytest ====
[tool.pytest.ini_options]
minversion = "6.0"
addopts = "--ds=config.settings.test --reuse-db --import-mode=importlib"
python_files = [
    "tests.py",
    "test_*.py",
]

# ==== Coverage ====
[tool.coverage.run]
include = ["income/**"]
omit = ["*/migrations/*", "*/tests/*"]
plugins = ["django_coverage_plugin"]

# ==== mypy ====
[tool.mypy]
python_version = "3.11"
check_untyped_defs = true
ignore_missing_imports = true
warn_unused_ignores = true
warn_redundant_casts = true
warn_unused_configs = true
plugins = [
    "mypy_django_plugin.main",
    "mypy_drf_plugin.main",
]

[[tool.mypy.overrides]]
# Django migrations should not produce any errors:
module = "*.migrations.*"
ignore_errors = true

[tool.django-stubs]
django_settings_module = "config.settings.test"

# ==== djLint ====
[tool.djlint]
blank_line_after_tag = "load,extends"
close_void_tags = true
format_css = true
format_js = true
# TODO: remove T002 when fixed https://github.com/djlint/djLint/issues/687
ignore = "H006,H030,H031,T002"
include = "H017,H035"
indent = 2
max_line_length = 119
profile = "django"

[tool.djlint.css]
indent_size = 2

[tool.djlint.js]
indent_size = 2

[tool.ruff]
target-version = "py312"
# Exclude a variety of commonly ignored directories.
extend-exclude = [
    "*/migrations/*.py",
    "staticfiles/*",
]

[tool.ruff.lint]
select = [
  "F",
  "E",
  "W",
  "C90",
  "I",
  "N",
  "UP",
  "YTT",
  # "ANN", # flake8-annotations: we should support this in the future but 100+ errors atm
  "ASYNC",
  "S",
  "BLE",
  "FBT",
  "B",
  "A",
  "COM",
  "C4",
  "DTZ",
  "T10",
  "DJ",
  "EM",
  "EXE",
  "FA",
  'ISC',
  "ICN",
  "G",
  'INP',
  'PIE',
  "T20",
  'PYI',
  'PT',
  "Q",
  "RSE",
  "RET",
  "SLF",
  "SLOT",
  "SIM",
  "TID",
  "TC",
  "INT",
  # "ARG", # Unused function argument
  "PTH",
  "ERA",
  "PD",
  "PGH",
  "PL",
  "TRY",
  "FLY",
  # "NPY",
  # "AIR",
  "PERF",
  # "FURB",
  # "LOG",
  "RUF",
]
ignore = [
  "S101", # Use of assert detected https://docs.astral.sh/ruff/rules/assert/
  "RUF012", # Mutable class attributes should be annotated with `typing.ClassVar`
  "SIM102", # sometimes it's better to nest
  "UP038", # Checks for uses of isinstance/issubclass that take a tuple
          # of types for comparison.
          # Deactivated because it can make the code slow:
          # https://github.com/astral-sh/ruff/issues/7871
]
# The fixes in extend-unsafe-fixes will require
# provide the `--unsafe-fixes` flag when fixing.
extend-unsafe-fixes = [
  "UP038",
]

[tool.ruff.lint.isort]
force-single-line = true

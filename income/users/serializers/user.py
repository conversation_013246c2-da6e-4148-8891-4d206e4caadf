from django.contrib.auth.hashers import make_password
from django.db import transaction
from django_redis import get_redis_connection
from rest_framework import serializers

from income import message
from income.users.models import Role
from income.users.models import User
from income.users.models import UserDepartment
from income.users.models import UserRole
from income.utils.menu_operation import create_role_permission
from income.utils.validators import email_validator
from income.utils.validators import mobile_phone_validator


class UsersSerializer(serializers.ModelSerializer):
    department_name = serializers.CharField(help_text="部门名称", read_only=True)
    state = serializers.CharField(help_text="用户状态", read_only=True)
    email = serializers.CharField(help_text="用户邮箱", validators=[email_validator])
    mobile = serializers.CharField(
        help_text="手机号",
        validators=[mobile_phone_validator],
    )
    role_ids = serializers.ListSerializer(
        help_text="所选角色列表",
        child=serializers.IntegerField(),
        write_only=True,
    )

    class Meta:
        model = User
        fields = [
            "id",
            "username",
            "password",
            "email",
            "mobile",
            "department_name",
            "state",
            "role_ids",
        ]
        extra_kwargs = {
            "password": {"write_only": True},
        }

    def validate_role_ids(self, value):
        all_role_id = Role.objects.all().values_list("id", flat=True)
        if not set(value).issubset(set(all_role_id)):
            raise serializers.ValidationError(message.ROLE_NOT_FOUND)
        return value

    def create(self, validated_data):
        validated_data["department_id"] = self.context["view"].kwargs["department_id"]
        validated_data["password"] = make_password(validated_data.pop("password"))
        role_ids = validated_data.pop("role_ids")
        instance = self.Meta.model.objects.create(**validated_data)
        # 开启事务进行信息创建
        with transaction.atomic():
            UserRole.objects.bulk_create(
                [
                    UserRole(
                        user_id=instance.id,
                        role_id=role_id,
                    )
                    for role_id in role_ids
                ],
                batch_size=50,
            )
        return instance


class UserUpdateSerializer(serializers.ModelSerializer):
    role_ids = serializers.ListSerializer(
        help_text="所选角色列表",
        child=serializers.IntegerField(),
        write_only=True,
    )

    class Meta:
        model = User
        fields = (
            "username",
            "email",
            "mobile",
            "role_ids",
        )

    def validate_role_ids(self, value):
        all_role_id = Role.objects.all().values_list("id", flat=True)
        if not set(value).issubset(set(all_role_id)):
            raise serializers.ValidationError(message.ROLE_NOT_FOUND)
        return value

    @staticmethod
    def update_permissions(instance):
        redis_client = get_redis_connection()
        if not redis_client.exists(f"identify:user-{instance.id}"):
            return
        create_role_permission(instance.user_roles, instance.id)

    def update(self, instance, validated_data):
        role_ids = validated_data.pop("role_ids")
        # 更新角色基本信息
        for k, v in validated_data.items():
            setattr(instance, k, v)
        instance.save()
        # 如果授权的角色没有变动
        exists_roles = instance.user_roles
        if set(role_ids) == set(exists_roles):
            return instance
        # 开启事务进行UserRole信息更新
        with transaction.atomic():
            # 删除取消授权的角色
            UserRole.objects.filter(
                user_id=instance.id,
                role_id__in=set(exists_roles) - set(role_ids),
            ).delete()
            # 新增新授权的角色
            UserRole.objects.bulk_create(
                [
                    UserRole(
                        user_id=instance.id,
                        role_id=role_id,
                    )
                    for role_id in set(role_ids) - set(exists_roles)
                ],
                batch_size=50,
            )
            # redis更新该用户的权限信息
            self.update_permissions(instance)

        return instance


class DepartmentSerializer(serializers.ModelSerializer):
    children = serializers.ListField(child=serializers.DictField())

    class Meta:
        model = UserDepartment
        fields = ("id", "department_name", "children")


class DepartmentCreateSerializer(serializers.ModelSerializer):
    """部门创建序列化器"""

    parent_id = serializers.IntegerField(
        help_text="父级ID",
        min_value=1,
        allow_null=True,
    )

    class Meta:
        model = UserDepartment
        fields = ("department_name", "parent_id")

    def validate_parent_id(self, value):
        if value is None or value == "":
            return None

        if not UserDepartment.objects.filter(pk=value).exists():
            raise serializers.ValidationError(message.DEPARTMENT_NOT_FOUND)
        return value


class DepartmentUpdateSerializer(serializers.ModelSerializer):
    class Meta:
        model = UserDepartment
        fields = ("department_name",)

from django.urls import include
from django.urls import path
from rest_framework.routers import SimpleRouter

from .views import MenuViewSet
from .views import RoleViewSet
from .views import UserDepartmentViewSet
from .views import UsersViewSet
from .views import login
from .views import logout
from .views import profile

router = SimpleRouter(trailing_slash=False)
router.register("departments", UserDepartmentViewSet, basename="departments")
router.register("roles", RoleViewSet, basename="roles")
router.register("menus", MenuViewSet, basename="menus")

user_router = SimpleRouter(trailing_slash=False)
user_router.register("users", UsersViewSet, basename="users")

urlpatterns = [
    path("login", login, name="login"),
    path("logout", logout, name="logout"),
    path("auth/check", profile, name="profile"),
    path("departments/<int:department_id>/", include(user_router.urls)),
    *router.urls,
]

from collections import defaultdict

from drf_spectacular.utils import extend_schema
from drf_spectacular.utils import extend_schema_view
from rest_framework import mixins
from rest_framework.response import Response

from income.contrib.drf.views import GenericViewSet
from income.permissions import IsAuthenticated
from income.users.models import Menu
from income.users.serializers import ProfileMenuSerializer


@extend_schema_view(
    list=extend_schema(summary="获取菜单信息"),
)
class MenuViewSet(
    GenericViewSet,
    mixins.ListModelMixin,
):
    permission_classes = [IsAuthenticated]
    pagination_class = None
    serializer_class = ProfileMenuSerializer

    def get_queryset(self):
        return Menu.objects.exclude(menu_name="ALL")

    def list(self, request, *args, **kwargs):
        fields = ["id", "menu_name", "router", "icon", "parent_id", "created_at"]
        menu_info_list = self.filter_queryset(self.get_queryset()).values(*fields)

        menus = []
        parent_map = defaultdict(list)  # 父级id对应的子级列表
        for menu_info in menu_info_list:
            parent_id = menu_info["parent_id"]
            data_dict = {
                "id": menu_info["id"],
                "menu_name": menu_info["menu_name"],
                "router": menu_info["router"],
                "icon": menu_info["icon"],
                "created_at": menu_info["created_at"],
            }
            if parent_id is None:
                menus.append(data_dict)
            else:
                parent_map[parent_id].append(data_dict)

        def get_child_data(info):
            child_data_list = parent_map.get(info["id"])
            info["children"] = child_data_list or None
            if child_data_list:
                for child_data in child_data_list:
                    get_child_data(child_data)

        for menu in menus:
            get_child_data(menu)

        return Response(menus)

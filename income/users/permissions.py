from rest_framework import exceptions
from rest_framework.permissions import BasePermission

from income import message
from .models import UserDepartment


class DepartmentBelowPermission(BasePermission):
    def has_permission(self, request, view):
        # 查询该部门是否存在
        if not UserDepartment.objects.filter(pk=view.kwargs["department_id"]).exists():
            raise exceptions.NotFound(message.NOT_FOUND)
        return True

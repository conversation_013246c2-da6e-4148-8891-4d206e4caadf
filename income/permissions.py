from django_redis import get_redis_connection
from rest_framework import exceptions
from rest_framework.permissions import BasePermission
from rest_framework.permissions import IsAuthenticated as BaseIsAuthenticated

from income import const


class IsAuthenticated(BaseIsAuthenticated):

    def has_permission(self, request, view):
        if super().has_permission(request, view):
            if not request.user.state == const.UserState.ENABLE:
                raise exceptions.PermissionDenied(detail="该用户已被停用请重新授权")
            return True
        return False


class RoleMenuPermission(BasePermission):
    def has_permission(self, request, view):
        # 超管拥有所有权限
        if request.user.is_admin:
            return True

        menu_identify = getattr(view, "identify", None)

        redis = get_redis_connection()
        # 判断该角色是否拥有当前菜单的访问权限
        if not redis.sismember(f"identify:user-{request.user.id}", menu_identify):
            return False
        # 获取用户在当前菜单的操作权限
        operation = redis.hget(f"operation:user-{request.user.id}", menu_identify)
        # 如果请求的方法为POST、PUT、DELETE并且没有操作权限则返回False
        return not (
            request.method in ["POST", "PUT", "DELETE"]
            and operation.decode("utf-8") != "true"
        )

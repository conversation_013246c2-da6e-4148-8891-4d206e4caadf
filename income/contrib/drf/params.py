import inspect

from functools import wraps
from rest_framework import serializers


def query_params_fbv_validator(validator):
    """
    校验query_params，可以通过序列化器或者函数来校验
    """
    assert isinstance(validator, serializers.SerializerMetaclass) or inspect.isfunction(
        validator
    ), "type of validator is serializers.Serializer or function"

    def _params(fn):
        @wraps(fn)
        def wrapper(request, *args, **kwargs):
            if isinstance(validator, serializers.SerializerMetaclass):
                serializer = validator(
                    data=request.query_params,
                    context={"request": request, "args": args, "kwargs": kwargs},
                )
                serializer.is_valid(raise_exception=True)
                setattr(request, "validate_query_params", serializer.validated_data)
            else:
                validator(request, *args, **kwargs)
            return fn(request, *args, **kwargs)

        return wrapper

    return _params


def query_params_cbv_validator(validator):
    """
    校验query_params，可以通过序列化器或者函数来校验
    """
    assert isinstance(validator, serializers.SerializerMetaclass) or inspect.isfunction(
        validator
    ), "type of validator is serializers.Serializer or function"

    def _params(method):
        @wraps(method)
        def wrapper(self, request, *args, **kwargs):
            if isinstance(validator, serializers.SerializerMetaclass):
                serializer = validator(
                    data=request.query_params,
                    context={"request": request, "args": args, "kwargs": kwargs},
                )
                serializer.is_valid(raise_exception=True)
                setattr(request, "validate_query_params", serializer.validated_data)
            else:
                validator(self, request, *args, **kwargs)
            return method(self, request, *args, **kwargs)

        return wrapper

    return _params

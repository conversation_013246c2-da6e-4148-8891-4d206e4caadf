from collections import OrderedDict

from django.core.paginator import Paginator
from django.utils.functional import cached_property
from rest_framework.pagination import PageNumberPagination as BasePageNumberPagination
from rest_framework.response import Response


class PageNumberPagination(BasePageNumberPagination):
    page_size_query_param = "pageSize"

    def get_paginated_response(self, data):
        return Response(
            OrderedDict(
                [
                    (
                        "page",
                        {
                            "current": self.page.number,
                            "size": self.page.paginator.per_page,
                            "total": self.page.paginator.count,
                        },
                    ),
                    ("records", data),
                ],
            ),
        )

    def get_paginated_response_schema(self, schema):
        return {
            "type": "object",
            "properties": {
                "page": {
                    "type": "object",
                    "properties": {
                        "total": {
                            "type": "integer",
                            "example": 100,
                        },
                        "current": {
                            "type": "integer",
                            "example": 1,
                        },
                        "size": {
                            "type": "integer",
                            "example": 20,
                        },
                    },
                },
                "records": schema,
            },
        }


class FasterDjangoPaginator(Paginator):
    @cached_property
    def count(self):
        if getattr(self.object_list, "page_count", None) is not None:
            return self.object_list.page_count
        return super().count


class FasterPageNumberPagination(PageNumberPagination):
    django_paginator_class = FasterDjangoPaginator

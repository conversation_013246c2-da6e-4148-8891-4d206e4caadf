from drf_spectacular.openapi import AutoSchema
from drf_spectacular.plumbing import ComponentRegistry


class CustomAutoSchema(AutoSchema):
    def get_operation(
        self,
        path: str,
        path_regex: str,
        path_prefix: str,
        method: str,
        registry: ComponentRegistry,
    ):
        """
        重写 get_operation 方法，对每个接口的响应格式进行统一处理。
        这里修改的是每个接口的 OpenAPI 文档响应部分
        """
        operation = super().get_operation(
            path, path_regex, path_prefix, method, registry
        )
        # 获取responses, 对其增加一层{"code": 0, "msg": "string", data: {}}
        if operation is None:
            return operation
        responses = operation.get("responses")
        if responses is None:
            return operation
        for code, response in responses.items():
            if code in ["200", "201"]:
                response["content"]["application/json"]["schema"] = {
                    "type": "object",
                    "properties": {
                        "code": {"type": "integer"},
                        "msg": {"type": "string"},
                        "data": response["content"]["application/json"]["schema"],
                    },
                }
        return operation

from textwrap import dedent

from drf_spectacular.authentication import SessionScheme
from drf_spectacular.utils import extend_schema
from drf_spectacular.views import SpectacularSwaggerView


class SpectacularSwaggerAutoAuthView(SpectacularSwaggerView):
    @extend_schema(exclude=True)
    def get(self, request, *args, **kwargs):
        response = super().get(request, *args, **kwargs)
        session_id = request.session.session_key
        if session_id:
            response.data["settings"] = f"""{{
                deepLinking: true,
                onComplete: function() {{
                    // Note the first arg must match whatever name of
                    // your cookie session id security scheme
                    ui.preauthorizeApiKey("Session ID", "{session_id}");
                }},
            }}"""
        return response


class SessionSchemeModified(SessionScheme):
    # Note that the list of available authorisations is sorted alphabetically by name
    # so if you want the authorisations options to show up in a particular order in
    # Swagger UI you must name them accordingly.
    name = "Session ID"
    priority = 1

    def get_security_definition(self, auto_schema):
        return {
            **super().get_security_definition(auto_schema),
            "description": dedent("""
                **This will be configured automatically in the Swagger UI
                documentation if there is currently a user logged in via
                the [login page](/api/local/login).**
            """),
        }


from drf_spectacular.authentication import OpenApiAuthenticationExtension
from drf_spectacular.types import OpenApiTypes

class SessionAuthenticationExtension(OpenApiAuthenticationExtension):
    target_class = "income.contrib.drf.authentication.SessionAuthentication"  # 你的自定义认证类路径
    name = "SessionAuth"  # OpenAPI中显示的安全方案名称

    def get_security_definition(self, auto_schema):
        return {
            "type": "apiKey",
            "in": "cookie",  # 如果使用Cookie传递Session
            "name": "sessionid",  # Django默认的Session Cookie名称
            "description": "Session-based authentication (Django default)",
        }

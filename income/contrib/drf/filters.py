import logging
import operator
from functools import reduce

from django.db import models
from django.db.models.constants import LOOKUP_SEP
from django.utils.encoding import force_str
from rest_framework.compat import coreapi
from rest_framework.compat import coreschema
from rest_framework.filters import BaseFilterBackend

from .utils import get_value
from .utils import make_markdown_table

logger = logging.getLogger("default")


class SearchFilter(BaseFilterBackend):
    """
    搜索过滤器
    search_param: 搜索值
    filter_column: 搜索字段
    """

    search_param = "filter"
    filter_column = "filterColumn"

    def filter_queryset(self, request, queryset, view):
        search_value = get_value(request, self.search_param)
        filter_column = get_value(request, self.filter_column)

        # 需要查询的字段,需要在ViewSet中配置
        search_fields = getattr(view, "search_fields", [])
        # 是否为模糊查询
        search_contains = getattr(view, "search_contains", False)
        fields_map = getattr(view, "fields_map", {})

        if search_value:
            if filter_column and (filter_column in search_fields):
                # 对某一字段进行搜索
                fields = [filter_column]
            else:
                # 对全部字段搜索
                fields = search_fields
            # 是否进行模糊搜索
            orm_lookups = (
                [
                    LOOKUP_SEP.join([fields_map.get(field, field), "icontains"])
                    for field in fields
                ]
                if search_contains
                else fields
            )
            queries = [
                models.Q(**{orm_lookup: search_value}) for orm_lookup in orm_lookups
            ]
            queryset = queryset.filter(reduce(operator.or_, queries))

        return queryset

    def get_schema_operation_parameters(self, view):
        search_fields = getattr(view, "search_fields", [])
        return [
            {
                "name": self.filter_column,
                "required": False,
                "in": "query",
                "title": force_str("搜索字段"),
                "description": force_str("需要搜索的字段,不传代表对全部字段进行搜索"),
                "schema": {
                    "type": "enum",
                    "enum": search_fields,
                },
            },
            {
                "name": self.search_param,
                "required": False,
                "in": "query",
                "title": force_str("搜索值"),
                "description": force_str("搜索值,大小写敏感"),
                "schema": {
                    "type": "string",
                },
            },
        ]


class SearchSingleFieldFilter(BaseFilterBackend):
    """
    搜索过滤器
    search_param: 搜索值
    """

    search_param = "word"

    def filter_queryset(self, request, queryset, view):
        search_value = get_value(request, self.search_param)
        # 需要查询的字段,需要在ViewSet中配置
        field_name = getattr(view, "search_single_field", None)
        if search_value and field_name:
            # 对某一字段进行搜索
            orm_lookup = LOOKUP_SEP.join([field_name, "icontains"])
            queryset = queryset.filter(models.Q(**{orm_lookup: search_value}))
        return queryset

    def get_schema_operation_parameters(self, view):
        return [
            {
                "name": self.search_param,
                "required": False,
                "in": "query",
                "description": force_str("搜索值,大小写敏感"),
                "schema": {
                    "type": "string",
                },
            },
        ]


class SearchAndFilter(BaseFilterBackend):
    """
    搜索过滤器
    search_param: 搜索值
    filter_column: 搜索字段
    """

    def filter_queryset(self, request, queryset, view):
        # 需要查询的字段,需要在ViewSet中配置
        search_fields = getattr(view, "search_fields", [])
        # 是否为模糊查询
        search_contains = getattr(view, "search_contains", False)

        query_params = getattr(request, "query_params", request.GET)
        value_mapping = {
            search_field: query_params.get(search_field, "").replace("\x00", "").strip()
            for search_field in search_fields
            if search_field in query_params
        }
        # 如果没有任何筛选条件, 返回原始查询
        if not any(value_mapping.values()):
            return queryset

        queries = []
        for search_field, value in value_mapping.items():
            if search_contains:
                queries.append(
                    models.Q(**{LOOKUP_SEP.join([search_field, "icontains"]): value}),
                )
            else:
                queries.append(models.Q(**{search_field: value}))

        return queryset.filter(reduce(operator.and_, queries))

    def get_schema_operation_parameters(self, view):
        search_fields = getattr(view, "search_fields", [])
        return [
            {
                "name": search_field,
                "required": False,
                "in": "query",
                "title": force_str(search_field),
                "description": force_str(search_field),
                "schema": {
                    "type": "string",
                },
            }
            for search_field in search_fields
        ]



class OrderingFilter(BaseFilterBackend):
    """
    排序过滤器
    order_column: 排序字段
    order_typeL 排序类型 desc - 降序 asc - 升序
    order_column_fields: view视图中定义的所能排序的字段
    """

    order_column = "orderColumn"
    order_type = "orderType"
    order_type_fields = ["desc", "asc"]

    order_column_fields = "order_column_fields"

    def filter_queryset(self, request, queryset, view):
        order_column = get_value(request, self.order_column)
        order_type = get_value(request, self.order_type)
        order_column_fields = getattr(view, self.order_column_fields, [])
        fields_map = getattr(view, "fields_map", {})

        if order_column in order_column_fields:
            if order_type in self.order_type_fields:
                order_column = fields_map.get(order_column, order_column)
                order_column = (
                    (order_type == "desc" and "-" + order_column) or order_column
                )
                queryset = queryset.order_by(order_column)
        return queryset

    def get_schema_operation_parameters(self, view):
        order_column_fields = getattr(view, self.order_column_fields, [])
        return [
            {
                "name": self.order_column,
                "required": False,
                "in": "query",
                "description": force_str("排序字段"),
                "schema": {
                    "type": "enum",
                    "enum": order_column_fields,
                },
            },
            {
                "name": self.order_type,
                "required": False,
                "in": "query",
                "description": force_str("排序类型"),
                "schema": {
                    "type": "enum",
                    "enum": self.order_type_fields,
                },
            },
        ]


class StatusFilter(BaseFilterBackend):
    """
    状态过滤器
    status_param: 状态参数
    status_choices: 状态的枚举
    """

    status_param = "status"
    status_choices = "status_choices"

    def filter_queryset(self, request, queryset, view):
        status = get_value(request, self.status_param)
        status_choices = getattr(view, self.status_choices)
        if status.isnumeric():
            if int(status) in status_choices.values:
                queryset = queryset.filter(status=status)
        return queryset

    def get_schema_operation_parameters(self, view):
        status_choices = getattr(view, self.status_choices)
        return [
            {
                "name": self.status_param,
                "required": False,
                "in": "query",
                "description": force_str(make_markdown_table(status_choices.choices)),
                "schema": {
                    "type": "enum",
                    "enum": status_choices.values,
                },
            },
        ]


class StatusesFilter(BaseFilterBackend):
    """
    多种状态过滤器
    status_param: 多状态参数
    status_choices: 状态的枚举
    """

    status_param = "status"
    status_choices = "status_choices"

    def filter_queryset(self, request, queryset, view):
        status = get_value(request, self.status_param).split(",")
        status = list(map(int, list(filter(str.isnumeric, status))))
        status_choices = getattr(view, self.status_choices)
        if status and not [x for x in status if x not in status_choices.values]:
            queryset = queryset.filter(status__in=status)
        return queryset

    def get_schema_fields(self, view):
        return [
            coreapi.Field(
                name=self.status_param,
                required=False,
                location="query",
                schema=coreschema.String(
                    title=force_str("多状态筛选"),
                    description=force_str("多状态筛选, 逗号分隔"),
                ),
            ),
        ]


class TypesFilter(BaseFilterBackend):
    """
    类型过滤器
    type_param: 类型参数
    type_choices: 类型的枚举
    """

    type_param = "type_param"
    type_choices = "type_choices"

    def filter_queryset(self, request, queryset, view):
        type_param = getattr(view, self.type_param)
        type_value = request.query_params.getlist(type_param, "")
        if not type_value:
            return queryset
        return queryset.filter(category__in=type_value)

    def get_schema_operation_parameters(self, view):
        self.type_param = getattr(view, self.type_param)
        type_choices = getattr(view, self.type_choices)
        return [
            {
                "name": self.type_param,
                "required": False,
                "in": "query",
                "description": force_str(make_markdown_table(type_choices.choices)),
                "schema": {
                    "type": "enum",
                    "enum": type_choices.values,
                },
            },
        ]


class BaseFilter(BaseFilterBackend):
    search_param = ""
    description = ""

    def get_schema_operation_parameters(self, view):
        return [
            {
                "name": self.search_param,
                "required": False,
                "in": "query",
                "description": force_str(self.description),
                "schema": {
                    "type": "string",
                },
            },
        ]

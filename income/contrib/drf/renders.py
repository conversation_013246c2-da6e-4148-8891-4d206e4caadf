from rest_framework import status
from rest_framework.renderers import BaseRenderer
from rest_framework.renderers import JSONRenderer


class IgnoreRenderer(BaseRenderer):
    media_type = "*/*"

    def render(self, data, accepted_media_type=None, renderer_context=None):
        return data


status_mapping = {
    status.HTTP_200_OK: "请求成功",
    status.HTTP_201_CREATED: "数据创建成功",
}


class CustomRenderer(JSONRenderer):
    def render(self, data, accepted_media_type=None, renderer_context=None):
        # 判断状态码不是200和201时, 返回失败, 200和201时返回成功并且状态码统一设置为200
        status_code = renderer_context["response"].status_code
        if status_code in status_mapping:
            status_code = status.HTTP_200_OK
            msg = status_mapping[status_code]
        else:
            msg = "请求失败"
        # 返回的格式
        response_data = {"code": status_code, "msg": msg, "data": data}
        return super().render(response_data, accepted_media_type, renderer_context)

from collections import defaultdict

from drf_spectacular.utils import extend_schema
from rest_framework.decorators import action
from rest_framework.response import Response

from income.contrib.drf.filters import SearchSingleFieldFilter
from income.contrib.drf.views import GenericViewSet

from .models import IncomeLevelStaticData
from .models import IncomeStaticData
from .serializers import StaticDataSimpleSerializer


class StaticDataViewSet(GenericViewSet):
    filter_backends = (SearchSingleFieldFilter,)
    search_single_field = "data_type"

    def get_queryset(self):
        return IncomeStaticData.objects.all()

    @extend_schema(
        tags=["static"],
        responses={200: StaticDataSimpleSerializer(many=True)},
        summary="仅展示id, data_value",
    )
    @action(
        methods=["get"],
        detail=False,
        url_path="simple-list",
        pagination_class=None,
    )
    def simple_list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(
            self.get_queryset(),
        ).values("id", "data_value")
        return Response(queryset)


class LevelStaticDataViewSet(StaticDataViewSet):
    filter_backends = (SearchSingleFieldFilter,)
    search_single_field = "data_type"

    def get_queryset(self):
        return IncomeLevelStaticData.objects.all()

    @extend_schema(
        tags=["static"],
        responses={200: StaticDataSimpleSerializer(many=True)},
        summary="仅展示id, data_value的层级数据",
    )
    @action(
        methods=["get"],
        detail=False,
        url_path="simple-list",
        pagination_class=None,
    )
    def simple_list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(
            self.get_queryset(),
        ).values("id", "data_value", "parent_id")

        result = []
        parent_map = defaultdict(list)  # 父级id对应的子级列表
        for item in queryset:
            parent_id = item["parent_id"]
            data_dict = {
                "id": item["id"],
                "data_value": item["data_value"],
            }
            if parent_id is None:
                result.append(data_dict)
            else:
                parent_map[parent_id].append(data_dict)

        def get_child_data(info):
            child_data_list = parent_map.get(info["id"])
            info["children"] = child_data_list or None
            if child_data_list:
                for child_data in child_data_list:
                    get_child_data(child_data)

        for res in result:
            get_child_data(res)
        return Response(result)


from django.db import models


class IncomeStaticData(models.Model):
    data_type = models.CharField(
        max_length=50,
        db_comment="数据类型(收入分类,公司实体等)",
    )
    data_code = models.CharField(
        max_length=50,
        blank=True,
        null=True,
        db_comment="枚举编码",
    )
    data_value = models.CharField(max_length=100, db_comment="枚举值")
    data_desc = models.CharField(max_length=500)
    state = models.CharField(max_length=1, db_comment="U:有效 E:失效")
    created_at = models.DateTimeField(db_comment="创建时间", auto_now_add=True)

    class Meta:
        managed = False
        db_table = "income_static_data"
        db_table_comment = "系统枚举数据字典表"
        ordering = ["-created_at"]

    def __str__(self):
        return f"<IncomeStaticData: {self.data_value}>"


class IncomeLevelStaticData(models.Model):
    """层级数据字典表"""

    data_type = models.CharField(max_length=50)
    data_value = models.Char<PERSON>ield(max_length=50)
    data_desc = models.Char<PERSON>ield(max_length=50, blank=True, null=True)
    remark = models.CharField(max_length=1000, blank=True, null=True)
    parent_id = models.IntegerField(blank=True, null=True)

    created_at = models.DateTimeField(db_comment="创建时间", auto_now_add=True)
    updated_at = models.DateTimeField(db_comment="更新时间", auto_now=True)

    class Meta:
        managed = False
        db_table = "income_level_static_data"
        db_table_comment = "层级数据字典表"

    def __str__(self):
        return f"<IncomeLevelStaticData: {self.data_value}>"

from django.urls import include
from django.urls import path
from rest_framework.routers import SimpleRouter

from .views import LevelStaticDataViewSet
from .views import StaticDataViewSet

router = SimpleRouter(trailing_slash=False)
router.register("static-data", StaticDataViewSet, basename="static-data")

level_router = SimpleRouter(trailing_slash=False)
level_router.register(
    "level-static-data",
    LevelStaticDataViewSet,
    basename="level-static-data",
)

urlpatterns = [
    path("", include(router.urls)),
    path("", include(level_router.urls)),
]

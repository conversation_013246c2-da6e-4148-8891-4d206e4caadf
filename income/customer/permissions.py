from rest_framework.permissions import BasePermission

from income import message
from income.contrib.drf.shortcuts import get_object_or_404

from .models import CustomerInfo


class CustomerPermission(BasePermission):
    """客户信息权限"""

    def has_permission(self, request, view):
        get_object_or_404(
            CustomerInfo.objects.all(),
            pk=view.kwargs["customer_id"],
            error_message=message.CUSTOMER_NOT_FOUND,
        )
        return True

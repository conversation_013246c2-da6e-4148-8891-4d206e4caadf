from rest_framework import serializers

from income import message
from income.customer.models import IncomeInvoiceInfo


class InvoiceInfoSerializer(serializers.ModelSerializer):
    """发票信息序列化器"""

    class Meta:
        model = IncomeInvoiceInfo
        exclude = ("updated_at",)
        read_only_fields = (
            "create_user",
            "status",
        )

    def validate_account_seq(self, value):
        """校验分账序号是否存在"""
        if self.instance and self.instance.account_seq == value:
            return value
        if IncomeInvoiceInfo.objects.filter(account_seq=value).exists():
            raise serializers.ValidationError(message.ACCPUNT_SEQ_NOT_FOUND)
        return value

    def validate(self, attrs):
        # 校验开账开始日期不能晚于结束日期
        charge_start_day = attrs.get("charge_start_day")
        charge_end_day = attrs.get("charge_end_day")
        if charge_start_day and charge_end_day and charge_start_day > charge_end_day:
            raise serializers.ValidationError(
                {"charge_start_day": message.INVALID_DATE_RANGE},
            )
        return attrs

    def create(self, validated_data):
        create_user = self.context["request"].user.username
        validated_data["create_user"] = create_user
        return self.Meta.model.objects.create(**validated_data)

    def update(self, instance, validated_data):
        request = self.context["request"]
        validated_data["create_user"] = request.user.username
        for k, v in validated_data.items():
            setattr(instance, k, v)
        instance.save()
        return instance


class InvoiceInfoSimpleSerializer(serializers.ModelSerializer):
    """发票信息简易序列化器"""

    class Meta:
        model = IncomeInvoiceInfo
        fields = ("id", "customer_invoice_name")

from rest_framework import exceptions
from rest_framework import serializers

from income import const
from income import message
from income.customer.models import ContactInfo
from income.customer.models import CustomerApproveHistory
from income.customer.models import CustomerInfo
from income.utils.validators import email_validator
from income.utils.validators import mobile_phone_validator
from income.utils.validators import phone_validator


class CustomersInfoSerializer(serializers.ModelSerializer):
    """客户信息"""

    class Meta:
        model = CustomerInfo
        exclude = ("deleted_at", "updated_at")
        read_only_fields = ("create_user", "approve_user")

    def validate_sale_name(self, value):
        if value not in ["张三", "李四"]:
            raise serializers.ValidationError(message.SALE_NAME_NOT_FOUND)
        # todo: 修改从User表的销售部门进行筛选查询
        # if not User.objects.filter(department_id=1, username=value).exists():
        #     raise serializers.ValidationError(message.SALE_NAME_NOT_FOUND)

        return value

    def create(self, validated_data):
        request = self.context["request"]
        validated_data["create_user"] = request.user.username
        validated_data["approve_state"] = const.ApproveState.INIT
        return self.Meta.model.objects.create(**validated_data)

    def update(self, instance, validated_data):
        request = self.context["request"]
        validated_data["create_user"] = request.user.username
        validated_data["approve_state"] = const.ApproveState.INIT
        for k, v in validated_data.items():
            setattr(instance, k, v)
        instance.save()
        return instance


class CustomerInfoSimpleSerializer(serializers.ModelSerializer):
    class Meta:
        model = CustomerInfo
        fields = ("id", "customer_name", "customer_num")


class CustomerApproveSerializer(serializers.Serializer):
    """客户信息审批序列化器

    action: 审批动作
        审批动作只会包含四种:
        submit: 提交审核, 状态变更为confirm
        revoke: 撤回, 状态变更为revoke
        approve: 审批通过, 状态变更为active
        reject: 驳回, 状态变更为back
    """

    id = serializers.IntegerField(help_text="客户信息id", required=True)
    action = serializers.ChoiceField(
        help_text="审批动作",
        choices=const.ApproveAction.choices,
        required=True,
    )
    reason = serializers.CharField(
        help_text="审批意见",
        required=False,
        allow_blank=True,
        allow_null=True,
    )

    def validate(self, attrs):
        customer_id = attrs.get("id")
        action = attrs.get("action")

        try:
            customer = CustomerInfo.objects.select_for_update().get(pk=customer_id)
        except CustomerInfo.DoesNotExist:
            raise exceptions.NotFound(message.CUSTOMER_NOT_FOUND)  # noqa: B904

        # 验证状态转换的合法性
        if (
            action == const.ApproveAction.SUBMIT
            and customer.approve_state != const.ApproveState.INIT
        ):
            raise serializers.ValidationError(
                {
                    "action": message.CUSTOMER_APPROVE_SUBMIT_ERROR,
                },
            )
        if (
            action == const.ApproveAction.REVOKE
            and customer.approve_state != const.ApproveState.CONFIRM
        ):
            raise serializers.ValidationError(
                {
                    "action": message.CUSTOMER_APPROVE_REVOKE_ERROR,
                },
            )
        if (
            action in [const.ApproveAction.APPROVE, const.ApproveAction.REJECT]
            and customer.approve_state != const.ApproveState.CONFIRM
        ):
            raise serializers.ValidationError(
                {
                    "action": message.CUSTOMER_APPROVE_APPROVE_ERROR,
                },
            )

        # 添加客户实例到attrs中
        attrs["customer_instance"] = customer
        return attrs


class ContactInfoSerializer(serializers.ModelSerializer):
    """客户联系人信息"""

    phone = serializers.CharField(required=False, validators=[phone_validator])
    mobile_phone = serializers.CharField(
        required=False,
        validators=[mobile_phone_validator],
    )
    email = serializers.EmailField(required=False, validators=[email_validator])

    class Meta:
        model = ContactInfo
        exclude = ("created_at", "updated_at")
        read_only_fields = ("create_user", "customer_id", "state")

    def create(self, validated_data):
        request = self.context["request"]
        validated_data["create_user"] = request.user.username
        validated_data["state"] = const.State.VALID
        return self.Meta.model.objects.create(**validated_data)

    def update(self, instance, validated_data):
        request = self.context["request"]
        validated_data["create_user"] = request.user.username
        for k, v in validated_data.items():
            setattr(instance, k, v)
        instance.save()
        return instance


class CustomerApproveHistorySerializer(serializers.ModelSerializer):
    """客户审批历史记录序列化器"""

    class Meta:
        model = CustomerApproveHistory
        exclude = ("id", "customer_id")


class CustomersInfoApproveSerializer(serializers.ModelSerializer):
    """客户信息审批序列化器"""

    class Meta:
        model = CustomerInfo
        exclude = ("deleted_at", "updated_at")

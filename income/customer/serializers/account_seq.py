from datetime import datetime

from django.db.models import Max
from rest_framework import serializers

from income import message
from income.customer.models import CustomerInfo
from income.customer.models import IncomeAccountSeq


class AccountSeqSerializer(serializers.ModelSerializer):
    customer_id = serializers.IntegerField(write_only=True, help_text="客户ID")

    class Meta:
        model = IncomeAccountSeq
        fields = "__all__"
        read_only_fields = (
            "account_seq",
            "customer_num",
            "customer_name",
            "created_at",
        )

    def validate_customer_id(self, value):
        if not CustomerInfo.objects.filter(id=value).exists():
            raise serializers.ValidationError(message.CUSTOMER_NOT_FOUND)
        return value

    @staticmethod
    def generate_account_seq():
        current_time = datetime.now().strftime("%Y%m")
        max_account_seq = IncomeAccountSeq.objects.filter(
            account_seq__startswith=f"FZ-{current_time}",
        ).aggregate(number_max=Max("account_seq"))
        max_num = max_account_seq.get("number_max")
        return (
            f"FZ-{current_time}{int(max_num[9:]) + 1:0>4}"
            if max_num
            else f"FZ-{current_time}0001"
        )

    def create(self, validated_data):
        validated_data["account_seq"] = self.generate_account_seq()
        customer = CustomerInfo.objects.values(
            "customer_num",
            "customer_name",
        ).get(id=validated_data.pop("customer_id"))
        validated_data["customer_num"] = customer["customer_num"]
        validated_data["customer_name"] = customer["customer_name"]
        return self.Meta.model.objects.create(**validated_data)


class AccountSeqSimpleSerializer(serializers.ModelSerializer):
    class Meta:
        model = IncomeAccountSeq
        fields = ("id", "account_seq", "seq_name")

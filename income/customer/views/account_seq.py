from drf_spectacular.utils import extend_schema
from drf_spectacular.utils import extend_schema_view
from rest_framework import mixins
from rest_framework.decorators import action
from rest_framework.response import Response

from income.contrib.drf.filters import SearchAndFilter
from income.contrib.drf.views import GenericViewSet
from income.customer.filters import CustomerNumberFilter
from income.customer.models import IncomeAccountSeq
from income.customer.serializers import AccountSeqSerializer
from income.customer.serializers import AccountSeqSimpleSerializer
from income.permissions import IsAuthenticated
from income.permissions import RoleMenuPermission


@extend_schema_view(
    list=extend_schema(summary="获取分账序号的信息"),
    create=extend_schema(summary="新建分账序号"),
    simple_list=extend_schema(
        responses={200: AccountSeqSimpleSerializer(many=True)},
        summary="仅展示分账序号ID、序号、名称",
    ),
)
@extend_schema(tags=["customer-account-seq"])
class AccountSeqViewSet(
    GenericViewSet,
    mixins.ListModelMixin,
    mixins.CreateModelMixin,
):
    serializer_class = AccountSeqSerializer
    permission_classes = [
        IsAuthenticated,
        RoleMenuPermission,
    ]

    filter_backends = [SearchAndFilter, CustomerNumberFilter]
    search_fields = ["seq_name", "account_seq"]
    search_contains = True

    def get_queryset(self):
        return IncomeAccountSeq.objects.all()

    @action(
        methods=["get"],
        detail=False,
        url_path="simple-list",
        filter_backends=[CustomerNumberFilter],
        pagination_class=None,
    )
    def simple_list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset()).values(
            "id",
            "account_seq",
            "seq_name",
        )
        return Response(queryset)

from drf_spectacular.utils import extend_schema
from drf_spectacular.utils import extend_schema_view
from rest_framework import mixins
from rest_framework.decorators import action
from rest_framework.response import Response

from income import const
from income import message
from income.contrib.drf.filters import SearchAndFilter
from income.contrib.drf.views import GenericViewSet
from income.contrib.drf.views import UpdateModelMixin
from income.customer.filters import AccountSeqFilter
from income.customer.models import IncomeInvoiceInfo
from income.customer.serializers import InvoiceInfoSerializer
from income.customer.serializers import InvoiceInfoSimpleSerializer
from income.permissions import IsAuthenticated
from income.permissions import RoleMenuPermission


@extend_schema_view(
    list=extend_schema(summary="获取发票信息"),
    create=extend_schema(summary="新建发票信息"),
    update=extend_schema(summary="编辑发票信息"),
)
@extend_schema(tags=["invoice-info"])
class InvoiceInfoViewSet(
    GenericViewSet,
    mixins.ListModelMixin,
    mixins.CreateModelMixin,
    UpdateModelMixin,
    mixins.RetrieveModelMixin,
):
    NOT_FOUND_MESSAGE = message.INVOICE_INFO_NOT_FOUND

    serializer_class = InvoiceInfoSerializer
    permission_classes = (IsAuthenticated, RoleMenuPermission)

    search_fields = [
        "customer_invoice_name",
        "create_user",
    ]
    filter_backends = (SearchAndFilter, AccountSeqFilter)
    search_contains = True

    identify = const.MenuIdentify.INVOICE_INFO

    def get_queryset(self):
        return IncomeInvoiceInfo.objects.all()

    @extend_schema(
        tags=["invoice-info"],
        responses={200: InvoiceInfoSimpleSerializer(many=True)},
        summary="仅展示发票ID、发票信息名称",
    )
    @action(
        methods=["get"],
        detail=False,
        url_path="simple-list",
        filter_backends=[AccountSeqFilter],
        pagination_class=None,
    )
    def simple_list(self, request, *args, **kwargs):
        queryset = (
            self.filter_queryset(self.get_queryset())
            .filter(
                status=const.AccountSeqStatus.APPROVED,
            )
            .values(
                "id",
                "customer_invoice_name",
            )
        )
        return Response(queryset)

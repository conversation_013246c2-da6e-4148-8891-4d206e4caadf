from .models import IncomeFeeTemplate


def add_extra_field(query_data_list: list):
    """费用实例模板list接口增加额外字段"""

    fee_template_code_list = [
        query_data["fee_template_code"] for query_data in query_data_list
    ]

    template_code_qs = IncomeFeeTemplate.objects.filter(
        fee_template_code__in=set(fee_template_code_list),
    ).values_list("fee_template_code", "is_level_fee")
    template_code_map = dict(template_code_qs)
    for query_data in query_data_list:
        query_data["is_level_fee"] = template_code_map.get(
            query_data["fee_template_code"],
        )
        query_data["created_at"] = query_data["created_at"].strftime("%s")
    return query_data_list

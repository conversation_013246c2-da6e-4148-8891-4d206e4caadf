from drf_spectacular.utils import extend_schema
from drf_spectacular.utils import extend_schema_view
from rest_framework import mixins
from rest_framework import status
from rest_framework.decorators import action
from rest_framework.exceptions import ParseError
from rest_framework.response import Response

from income import const
from income import message
from income.contrib.drf.filters import SearchFilter
from income.contrib.drf.views import GenericViewSet
from income.contrib.drf.views import UpdateModelMixin
from income.permissions import IsAuthenticated
from income.permissions import RoleMenuPermission

from .models import IncomeFeeInstance
from .models import IncomeFeeInstanceLevel
from .models import IncomeFeePackage
from .models import IncomeFeeTemplate
from .premissions import FeeInstancePermission
from .premissions import FeePackagePermission
from .serializers import IncomeFeeInstanceLevelSerializer
from .serializers import IncomeFeeInstanceSerializer
from .serializers import IncomeFeePackageSerializer
from .serializers import IncomeFeePackageSimpleSerializer
from .serializers import IncomeFeeTemplateSerializer
from .serializers import IncomeFeeTemplateSimpleSerializer
from .utils import add_extra_field


@extend_schema_view(
    list=extend_schema(summary="获取费用模板信息"),
    simple_list=extend_schema(summary="仅展示费用模板ID、费用模板名称、是否是阶梯计费"),
)
@extend_schema(tags=["fee"])
class IncomeFeeTemplateViewSet(
    GenericViewSet,
    mixins.ListModelMixin,
):
    """费用模板"""

    serializer_class = IncomeFeeTemplateSerializer
    permission_classes = [IsAuthenticated, RoleMenuPermission]
    filter_backends = [SearchFilter]
    search_fields = ["fee_template_name", "fee_template_code", "bill_rule"]
    search_contains = True

    identify = const.MenuIdentify.INCOME_FEE_TEMPLATE

    def get_queryset(self):
        return IncomeFeeTemplate.objects.all()

    @extend_schema(
        responses={200: IncomeFeeTemplateSimpleSerializer(many=True)},
    )
    @action(
        methods=["get"],
        detail=False,
        url_path="simple-list",
        filter_backends=[],
        pagination_class=None,
    )
    def simple_list(self, request, *args, **kwargs):
        queryset = (
            self.get_queryset()
            .filter(
                state=const.State.VALID,
            )
            .values(
                "id",
                "fee_template_name",
                "is_level_fee",
            )
        )
        return Response(queryset)


@extend_schema_view(
    list=extend_schema(summary="获取费用套餐信息"),
    create=extend_schema(summary="新建费用套餐信息"),
    update=extend_schema(summary="编辑费用套餐信息"),
    simple_list=extend_schema(summary="仅展示费用套餐ID、费用套餐名称"),
)
@extend_schema(tags=["fee-package"])
class IncomeFeePackageViewSet(
    GenericViewSet,
    mixins.ListModelMixin,
    mixins.CreateModelMixin,
    UpdateModelMixin,
):
    """费用套餐"""

    serializer_class = IncomeFeePackageSerializer
    permission_classes = [IsAuthenticated, RoleMenuPermission]
    filter_backends = [SearchFilter]
    search_fields = ["package_name"]
    search_contains = True

    identify = const.MenuIdentify.INCOME_FEE_PACKAGE

    def get_queryset(self):
        return IncomeFeePackage.objects.all()


    @extend_schema(
        responses={200: IncomeFeePackageSimpleSerializer(many=True)},
    )
    @action(
        methods=["get"],
        detail=False,
        url_path="simple-list",
        filter_backends=[],
        pagination_class=None,
    )
    def simple_list(self, request, *args, **kwargs):
        queryset = self.get_queryset().values("id", "package_name")
        return Response(queryset)


@extend_schema_view(
    list=extend_schema(summary="获取费用实例信息"),
    create=extend_schema(summary="新建费用实例信息"),
    update=extend_schema(summary="编辑费用实例信息"),
    retrieve=extend_schema(summary="获取费用实例的详细信息"),
)
@extend_schema(tags=["fee-instance"])
class IncomeFeeInstanceViewSet(
    GenericViewSet,
    mixins.CreateModelMixin,
    mixins.RetrieveModelMixin,
    UpdateModelMixin,
):
    NOT_FOUND_MESSAGE = message.FEE_INSTANCE_NOT_FOUND

    serializer_class = IncomeFeeInstanceSerializer
    permission_classes = [IsAuthenticated, FeePackagePermission, RoleMenuPermission]

    filter_backends = [SearchFilter]
    search_fields = ["fee"]
    search_contains = True

    identify = const.MenuIdentify.INCOME_FEE_PACKAGE

    def get_queryset(self):
        return IncomeFeeInstance.objects.filter(
            income_fee_package_id=self.kwargs["fee_package_id"],
        )

    def list(self, request, *args, **kwargs):
        fields = [field.name for field in IncomeFeeInstance._meta.fields]  # noqa: SLF001
        fields.remove("updated_at")
        fields.remove("income_fee_package_id")
        queryset = self.filter_queryset(
            self.get_queryset(),
        ).values(*fields)
        page = self.paginate_queryset(queryset)
        if page is not None:
            data = add_extra_field(page)
            return self.get_paginated_response(data)
        data = add_extra_field(queryset)
        return Response(data)


@extend_schema_view(
    list=extend_schema(summary="获取费用实例等级信息"),
    create=extend_schema(summary="新建费用实例等级信息"),
    update=extend_schema(summary="编辑费用实例等级信息"),
)
@extend_schema(tags=["fee-instance-level"])
class IncomeFeeInstanceLevelViewSet(
    GenericViewSet,
    mixins.ListModelMixin,
    UpdateModelMixin,
):
    """费用实例等级"""

    NOT_FOUND_MESSAGE = message.FEE_INSTANCE_LEVEL_NOT_FOUND

    serializer_class = IncomeFeeInstanceLevelSerializer
    permission_classes = [IsAuthenticated, FeeInstancePermission, RoleMenuPermission]

    filter_backends = [SearchFilter]
    search_fields = ["level", "fee"]
    search_contains = True

    identify = const.MenuIdentify.INCOME_FEE_PACKAGE

    def get_queryset(self):
        return IncomeFeeInstanceLevel.objects.filter(
            fee_instance_id=self.kwargs["fee_instance_id"],
        )

    def create(self, request, *args, **kwargs):
        # 校验是否是阶梯计费模板
        fee_template_code = IncomeFeeInstance.objects.values_list(
            "fee_template_code",
            flat=True,
        ).get(pk=self.kwargs["fee_instance_id"])
        is_level_fee = (
            IncomeFeeTemplate.objects.filter(
                fee_template_code=fee_template_code,
                state=const.State.VALID,
            )
            .values_list("is_level_fee", flat=True)
            .first()
        )
        if is_level_fee != 1:
            raise ParseError(message.FEE_TEMPLATE_NOT_LEVEL_FEE)
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response(serializer.data, status=status.HTTP_201_CREATED)

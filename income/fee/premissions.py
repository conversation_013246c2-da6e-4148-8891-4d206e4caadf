from rest_framework.permissions import BasePermission

from income import message
from income.contrib.drf.shortcuts import get_object_or_404

from .models import IncomeFeeInstance
from .models import IncomeFeePackage


class FeePackagePermission(BasePermission):
    """费用套餐权限"""

    def has_permission(self, request, view):
        get_object_or_404(
            IncomeFeePackage.objects.all(),
            pk=view.kwargs["fee_package_id"],
            error_message=message.FEE_PACKAGE_NOT_FOUND,
        )
        return True


class FeeInstancePermission(BasePermission):
    """费用实例权限"""

    def has_permission(self, request, view):
        get_object_or_404(
            IncomeFeeInstance.objects.all(),
            pk=view.kwargs["fee_instance_id"],
            error_message=message.FEE_INSTANCE_NOT_FOUND,
        )
        return True

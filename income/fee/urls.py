from django.urls import include
from django.urls import path
from rest_framework.routers import SimpleRouter

from .views import IncomeFeeInstanceLevelViewSet
from .views import IncomeFeeInstanceViewSet
from .views import IncomeFeePackageViewSet
from .views import IncomeFeeTemplateViewSet

router = SimpleRouter(trailing_slash=False)
router.register("fee-templates", IncomeFeeTemplateViewSet, basename="fee_template")

package_router = SimpleRouter(trailing_slash=False)
package_router.register(
    "fee-packages",
    IncomeFeePackageViewSet,
    basename="fee_package",
)

instance_router = SimpleRouter(trailing_slash=False)
instance_router.register(
    "fee-instances",
    IncomeFeeInstanceViewSet,
    basename="fee_instance",
)

level_router = SimpleRouter(trailing_slash=False)
level_router.register(
    "fee-instance-levels",
    IncomeFeeInstanceLevelViewSet,
    basename="fee_instance_level",
)

urlpatterns = [
    path("", include(router.urls)),
    path("", include(package_router.urls)),
    path("fee-packages/<int:fee_package_id>/", include(instance_router.urls)),
    path(
        "fee-instances/<int:fee_instance_id>/",
        include(level_router.urls),
    ),
]

from rest_framework import serializers

from income import const
from income import message

from .models import IncomeFeeInstance
from .models import IncomeFeeInstanceLevel
from .models import IncomeFeePackage
from .models import IncomeFeeTemplate


class IncomeFeeTemplateSerializer(serializers.ModelSerializer):
    class Meta:
        model = IncomeFeeTemplate
        fields = "__all__"


class IncomeFeeTemplateSimpleSerializer(serializers.ModelSerializer):
    class Meta:
        model = IncomeFeeTemplate
        fields = ("id", "fee_template_name", "is_level_fee")


class IncomeFeePackageSerializer(serializers.ModelSerializer):
    class Meta:
        model = IncomeFeePackage
        exclude = ["updated_at"]
        read_only_fields = ["create_user"]

    def validate_package_name(self, value):
        if self.instance and self.instance.package_name == value:
            return value
        if self.Meta.model.objects.filter(package_name=value).exists():
            raise serializers.ValidationError(message.FEE_PACKAGE_NAME_EXISTS)
        return value

    def create(self, validated_data):
        validated_data["create_user"] = self.context["request"].user.username
        return super().create(validated_data)


class IncomeFeePackageSimpleSerializer(serializers.ModelSerializer):
    class Meta:
        model = IncomeFeePackage
        fields = ("id", "package_name")


class IncomeFeeInstanceSerializer(serializers.ModelSerializer):
    fee_template_id = serializers.IntegerField(help_text="费用模板ID", write_only=True)
    is_level_fee = serializers.IntegerField(help_text="是否是阶梯计费", read_only=True)

    class Meta:
        model = IncomeFeeInstance
        exclude = ["updated_at", "income_fee_package_id"]
        read_only_fields = ["fee_template_name", "fee_template_code", "state"]

    def validate(self, attrs):
        # 校验template_id是否存在 && 并且模板状态为 U
        template_id = attrs.pop("fee_template_id")
        template = IncomeFeeTemplate.objects.filter(
            pk=template_id,
            state=const.State.VALID,
        ).first()

        if not template:
            raise serializers.ValidationError(
                {"fee_template_id": message.FEE_TEMPLATE_NOT_FOUND},
            )

        # 如果是更新操作,检查模板变更时的等级数据约束
        if (
            self.instance
            and self.instance.fee_template_code != template.fee_template_code
        ):
            # 检查新模板是否支持阶梯计费
            if template.is_level_fee != 1:
                # 检查当前费用实例是否存在费用实例等级数据
                if IncomeFeeInstanceLevel.objects.filter(
                    fee_instance_id=self.instance.id,
                ).exists():
                    raise serializers.ValidationError(
                        {"fee_template_id": message.FEE_INSTANCE_HAS_LEVEL_DATA},
                    )

        # 设置模板名称和模板编号
        attrs["fee_template_name"] = template.fee_template_name
        attrs["fee_template_code"] = template.fee_template_code
        return attrs

    def create(self, validated_data):
        validated_data["income_fee_package_id"] = self.context["view"].kwargs[
            "fee_package_id"
        ]
        return super().create(validated_data)


class IncomeFeeInstanceLevelSerializer(serializers.ModelSerializer):
    class Meta:
        model = IncomeFeeInstanceLevel
        exclude = ["updated_at", "fee_instance_id"]
        read_only_fields = ["state"]

    def create(self, validated_data):
        validated_data["fee_instance_id"] = self.context["view"].kwargs[
            "fee_instance_id"
        ]
        return super().create(validated_data)

from django.db import models

from income import const


# Create your models here.
class IncomeFeeTemplate(models.Model):
    fee_template_code = models.IntegerField(
        db_comment="费用类型",
    )
    fee_template_name = models.CharField(
        max_length=30,
        db_comment="费用类别名称",
    )
    bill_rule = models.CharField(
        max_length=2000,
        blank=True,
        null=True,
        db_comment="计费规则",
    )
    own_business = models.IntegerField(
        choices=const.OwnBusiness.choices,
        db_comment="0:普通; 1:语音; 2:流量",
    )
    is_level_fee = models.IntegerField(
        db_comment="是否是阶梯计费: 0:是; 1:不是",
    )
    state = models.CharField(
        max_length=1,
        choices=const.State.choices,
        default=const.State.VALID,
        db_comment="U:有效 E:失效",
    )

    created_at = models.DateTimeField(db_comment="创建时间", auto_now_add=True)

    class Meta:
        managed = False
        db_table = "income_fee_template"
        ordering = ["created_at"]

    def __str__(self):
        return f"<IncomeFeeTemplate: {self.fee_template_name}>"


class IncomeFeePackage(models.Model):
    package_name = models.CharField(
        max_length=255,
        db_comment="套餐名称",
    )
    remark = models.CharField(
        max_length=2000,
        blank=True,
        null=True,
        db_comment="套餐说明",
    )
    create_user = models.CharField(max_length=20, db_comment="创建者")

    created_at = models.DateTimeField(db_comment="创建时间", auto_now_add=True)
    updated_at = models.DateTimeField(db_comment="更新时间", auto_now=True)

    class Meta:
        managed = False
        db_table = "income_fee_package"
        ordering = ["-created_at"]

    def __str__(self):
        return f"<IncomeFeePackage: {self.package_name}>"


class IncomeFeeInstance(models.Model):
    """费用实例"""

    income_fee_package_id = models.IntegerField(db_comment="套餐ID")
    fee_template_code = models.IntegerField(db_comment="费用模板编号")
    fee_template_name = models.CharField(max_length=30, db_comment="费用模板名称")
    fee = models.DecimalField(
        max_digits=10,
        decimal_places=4,
        db_comment="费用金额",
        default=0.0000,
    )
    include_amount = models.IntegerField(
        blank=True,
        null=True,
        db_comment="包含分钟数或流量数",
    )
    over_fee = models.DecimalField(
        max_digits=10,
        decimal_places=4,
        db_comment="超出后流量单价或语音单价",
        default=0.0000,
    )
    fee_unit = models.CharField(
        max_length=20,
        blank=True,
        null=True,
        db_comment="计费单位",
    )
    speed = models.CharField(max_length=20, blank=True, null=True, db_comment="速率")
    state = models.CharField(
        max_length=1,
        db_comment="U:有效 E:失效",
        choices=const.State.choices,
        default=const.State.VALID,
    )
    priority = models.IntegerField(blank=True, null=True, db_comment="优先级")
    discount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        blank=True,
        null=True,
        db_comment="折扣",
    )
    min_fee = models.IntegerField(blank=True, null=True, db_comment="最低消费")
    max_fee = models.IntegerField(blank=True, null=True, db_comment="封顶费用")
    remark = models.CharField(max_length=512, blank=True, null=True, db_comment="备注")

    created_at = models.DateTimeField(db_comment="创建时间", auto_now_add=True)
    updated_at = models.DateTimeField(db_comment="更新时间", auto_now=True)

    class Meta:
        managed = False
        db_table = "income_fee_instance"
        ordering = ["-created_at"]

    def __str__(self):
        return f"<IncomeFeeInstance: {self.fee_template_name}>"


class IncomeFeeInstanceLevel(models.Model):
    """费用实例等级"""

    fee_instance_id = models.IntegerField(db_comment="费用实例ID")
    fee = models.DecimalField(
        max_digits=10,
        decimal_places=4,
        db_comment="费用",
    )
    fee_unit = models.CharField(
        max_length=20,
        blank=True,
        null=True,
        db_comment="费用单位",
    )
    month_min = models.IntegerField(blank=True, null=True)
    month_max = models.IntegerField(blank=True, null=True)
    month_nums = models.IntegerField(blank=True, null=True)
    level = models.IntegerField(db_comment="等级: 1/2/3/4/5")
    state = models.CharField(
        max_length=1,
        choices=const.State.choices,
        default=const.State.VALID,
        db_comment="U:有效 E:失效",
    )
    remark = models.CharField(max_length=1000, blank=True, null=True)

    created_at = models.DateTimeField(db_comment="创建时间", auto_now_add=True)
    updated_at = models.DateTimeField(db_comment="更新时间", auto_now=True)

    class Meta:
        managed = False
        db_table = "income_fee_instance_level"
        ordering = ["-created_at"]

    def __str__(self):
        return f"<IncomeFeeInstanceLevel: {self.fee_instance_id}>"

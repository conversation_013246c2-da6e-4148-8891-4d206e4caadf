from rest_framework import exceptions
from rest_framework import serializers

from income import const
from income import message
from income.order.models import IncomeOrderInfo

from .models import IncomeAdjustDetail
from .models import IncomeChargeDetail


class IncomeChargeDetailRawSerializer(serializers.ModelSerializer):
    """
    专门处理Raw SQL查询结果的序列化器
    Raw查询已经包含了所有关联字段,无需额外查询
    """

    customer_name = serializers.CharField(read_only=True, help_text="customer_name")
    sale_name = serializers.CharField(read_only=True, help_text="sale_name")
    sign_contract_entity = serializers.CharField(
        read_only=True,
        help_text="sign_contract_entity",
    )
    contract_legal_num = serializers.CharField(
        read_only=True,
        help_text="contract_legal_num",
    )
    adjust_month = serializers.CharField(read_only=True, help_text="adjust_month")
    adjust_reason_class = serializers.Char<PERSON><PERSON>(
        read_only=True,
        help_text="adjust_reason_class",
    )
    adjust_reason = serializers.Cha<PERSON><PERSON><PERSON>(read_only=True, help_text="adjust_reason")

    class Meta:
        model = IncomeChargeDetail
        exclude = ("updated_at",)


class IncomeAdjustDetailSerializer(serializers.ModelSerializer):
    class Meta:
        model = IncomeAdjustDetail
        exclude = ("updated_at",)


class IncomeChargeAdjustDetailSerializer(serializers.ModelSerializer):
    customer_name = serializers.CharField(read_only=True, help_text="customer_name")

    class Meta:
        model = IncomeChargeDetail
        fields = (
            "id",
            "customer_name",
            "batch_no",
            "order_no",
            "sub_order_no",
            "charge_month",
            "fee_amount",
            "income_type",
            "tax_type",
            "tax",
        )


class IncomeChargeAdjustCreateSerializer(serializers.ModelSerializer):
    class Meta:
        model = IncomeAdjustDetail
        exclude = ("state", "created_at", "updated_at", "adjust_type", "approve_user")
        read_only_fields = ("create_user",)

    def validate_adjust_charge_detail_id(self, value):
        if not IncomeChargeDetail.objects.filter(pk=value).exists():
            raise serializers.ValidationError(message.CHARGE_DETAIL_NOT_FOUND)
        return value

    def create(self, validated_data):
        """创建调账明细"""
        validated_data["create_user"] = self.context["request"].user.username
        validated_data["state"] = const.AdjustState.INITIAL_IMPORT
        return super().create(validated_data)


class IncomeAdjustApproveSerializer(serializers.Serializer):
    adjust_id = serializers.IntegerField(help_text="调账id", required=True)
    action = serializers.ChoiceField(
        help_text="审批动作",
        choices=const.AdjustApproveAction.choices,
        required=True,
    )
    adjust_type = serializers.ChoiceField(
        help_text="调账类型",
        choices=const.AdjustType.choices,
        required=True,
    )

    def validate(self, attrs):
        try:
            adjust_instance = IncomeAdjustDetail.objects.select_for_update().get(
                pk=attrs["adjust_id"],
            )
        except IncomeAdjustDetail.DoesNotExist:
            raise exceptions.NotFound(message.ADJUST_DETAIL_NOT_FOUND)  # noqa: B904
        # 验证状态转换的合法性, 调账记录为初始化信息导入时才可以进行审批操作
        if adjust_instance.state != const.AdjustState.INITIAL_IMPORT:
            raise serializers.ValidationError(
                {
                    "action": message.CHARGE_APPROVE_SUBMIT_ERROR,
                },
            )
        attrs["adjust_instance"] = adjust_instance
        return attrs


class IncomeOrderAdjustSerializer(serializers.ModelSerializer):
    main_customer_num = serializers.CharField(
        read_only=True,
        help_text="main_customer_num",
    )

    class Meta:
        model = IncomeOrderInfo
        fields = (
            "id",
            "order_num",
            "total_num",
            "income_type",
            "tax_type",
            "tax_rate",
            "main_customer_num",
            "bill_status",
        )


class IncomeOrderAdjustCreateSerializer(serializers.ModelSerializer):
    class Meta:
        model = IncomeAdjustDetail
        exclude = (
            "state",
            "created_at",
            "updated_at",
            "adjust_type",
            "adjust_charge_detail_id",
            "batch_no",
            "approve_user",
        )
        read_only_fields = ("create_user",)

    def create(self, validated_data):
        """创建调账明细"""
        validated_data["create_user"] = self.context["request"].user.username
        validated_data["state"] = const.AdjustState.INITIAL_IMPORT
        validated_data["adjust_type"] = const.AdjustType.NON_ACCRUAL
        return super().create(validated_data)

from django.urls import include
from django.urls import path
from rest_framework.routers import SimpleRouter

from .views import IncomeAdjustDetailViewSet
from .views import IncomeChargeAdjustDetailViewSet
from .views import IncomeChargeDetailViewSet
from .views import IncomeOrderAdjustViewInfoViewSet

router = SimpleRouter(trailing_slash=False)
router.register("charge-details", IncomeChargeDetailViewSet, basename="charge_details")

adjust_router = SimpleRouter(trailing_slash=False)
adjust_router.register(
    "adjust-details", IncomeAdjustDetailViewSet, basename="adjust_details",
)

charge_adjust_router = SimpleRouter(trailing_slash=False)
charge_adjust_router.register(
    "charge-adjust",
    IncomeChargeAdjustDetailViewSet,
    basename="charge_adjust",
)

income_adjust_router = SimpleRouter(trailing_slash=False)
income_adjust_router.register(
    "income-adjust",
    IncomeOrderAdjustViewInfoViewSet,
    basename="income_adjust",
)

urlpatterns = [
    path("", include(router.urls)),
    path("", include(adjust_router.urls)),
    path("", include(charge_adjust_router.urls)),
    path("", include(income_adjust_router.urls)),
]

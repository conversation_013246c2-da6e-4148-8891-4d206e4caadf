from django.db import models

from income import const


class IncomeChargeDetail(models.Model):
    """出账明细"""

    batch_no = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        db_comment="批次号",
    )
    order_no = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        db_comment="订单号",
    )
    sub_order_no = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        db_comment="子订单",
    )
    charge_month = models.IntegerField(blank=True, null=True, db_comment="账期")
    order_charge_begin = models.DateField(
        blank=True,
        null=True,
        db_comment="订单计费始日",
    )
    order_charge_end = models.DateField(
        blank=True,
        null=True,
        db_comment="订单计费终日",
    )
    tax_type = models.CharField(
        max_length=50,
        blank=True,
        null=True,
        db_comment="税率类型",
    )
    fee_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        blank=True,
        null=True,
        db_comment="出账金额",
    )
    pay_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        blank=True,
        null=True,
        db_comment="已收款金额",
    )
    unpay_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        blank=True,
        null=True,
        db_comment="未收款金额",
    )
    tax = models.IntegerField(blank=True, null=True, db_comment="税率")
    fee_type = models.CharField(
        max_length=20,
        blank=True,
        null=True,
        db_comment="计费类型: 月付/年付/一次性",
    )
    pay_type = models.CharField(
        max_length=20,
        blank=True,
        null=True,
        db_comment="付费类型: 预付/后付",
    )
    currency_type = models.CharField(
        max_length=10,
        blank=True,
        null=True,
        db_comment="币种",
    )
    income_type = models.CharField(
        max_length=40,
        blank=True,
        null=True,
        db_comment="收入分类",
    )
    speed = models.CharField(max_length=64, blank=True, null=True, db_comment="速率")
    charge_explain = models.CharField(
        max_length=255,
        blank=True,
        null=True,
        db_comment="资费说明",
    )
    a_info = models.CharField(
        max_length=255,
        blank=True,
        null=True,
        db_comment="a端信息",
    )
    product_main_category = models.CharField(
        max_length=64,
        blank=True,
        null=True,
        db_comment="产品主类",
    )
    product_sub_category = models.CharField(
        max_length=50,
        blank=True,
        null=True,
        db_comment="产品次型",
    )
    order_type = models.CharField(
        max_length=20,
        blank=True,
        null=True,
        db_comment="订单类型: 客户正式/供应商正式/自用/注销",
    )
    bill_template = models.CharField(
        max_length=50,
        blank=True,
        null=True,
        db_comment="出账模版/收入属性",
    )
    contract_num = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        db_comment="合同编号",
    )
    current_attr = models.CharField(
        max_length=10,
        blank=True,
        null=True,
        db_comment="当年业务属性",
    )
    customer_num = models.CharField(max_length=50, blank=True, null=True)
    account_seq = models.CharField(
        max_length=50,
        blank=True,
        null=True,
        db_comment="分账序号",
    )
    income_adjust_id = models.IntegerField(blank=True, null=True)
    charge_user = models.CharField(
        max_length=20,
        blank=True,
        null=True,
        db_comment="操作员",
    )
    charge_type = models.IntegerField(
        blank=True,
        null=True,
        choices=const.ChargeType.choices,
        db_comment="出账类型: 1:系统出账/2:调账/3:调税/4:外部费用导入",
    )
    bill_used_nums = models.JSONField(
        blank=True,
        null=True,
        db_comment="计费的分钟数/条数/流量等",
    )
    group_approve_state = models.CharField(
        max_length=20,
        blank=True,
        null=True,
        db_comment="集团审批状态",
    )
    need_invoice = models.IntegerField(
        blank=True,
        null=True,
        db_comment="是否需要开发票: 0-不需要, 1-需要",
    )

    created_at = models.DateTimeField(db_comment="创建时间", auto_now_add=True)
    updated_at = models.DateTimeField(db_comment="更新时间", auto_now=True)

    class Meta:
        managed = False
        db_table = "income_charge_detail"
        ordering = ["-created_at"]

    def __str__(self):
        return f"<IncomeChargeDetail: {self.order_no}>"


class IncomeAdjustDetail(models.Model):
    """调账明细"""

    batch_no = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        db_comment="批次号",
    )
    order_no = models.CharField(max_length=100, blank=True, null=True)
    sub_order_no = models.CharField(max_length=100, blank=True, null=True)
    charge_month = models.IntegerField(blank=True, null=True, db_comment="权责月")
    adjust_month = models.IntegerField(blank=True, null=True, db_comment="调账月")
    adjust_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        blank=True,
        null=True,
        db_comment="调账费用",
    )
    adjust_tax = models.IntegerField(blank=True, null=True)
    adjust_reason_class = models.CharField(
        max_length=50,
        blank=True,
        null=True,
        db_comment="调账分类",
    )
    adjust_reason = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        db_comment="调账原因",
    )
    state = models.IntegerField(
        blank=True,
        null=True,
        choices=const.AdjustState.choices,
        default=const.AdjustState.INITIAL_IMPORT,
        db_comment="状态: 0-初始化导入,1-审批通过,2-审批拒绝",
    )

    adjust_charge_detail_id = models.IntegerField(
        blank=True,
        null=True,
        db_comment="调整的权责id",
    )
    adjust_type = models.IntegerField(
        blank=True,
        null=True,
        choices=const.AdjustType.choices,
        default=const.AdjustType.ACCRUAL,
        db_comment="调账类别: 1-权责调账;2-无权责调账;3-外部费用导入调账",
    )
    approve_user = models.CharField(
        max_length=20,
        blank=True,
        null=True,
        db_comment="审批人",
    )
    create_user = models.CharField(max_length=40, blank=True, null=True)

    created_at = models.DateTimeField(db_comment="创建时间", auto_now_add=True)
    updated_at = models.DateTimeField(db_comment="更新时间", auto_now=True)

    class Meta:
        managed = False
        db_table = "income_adjust_detail"
        ordering = ["-created_at"]

    def __str__(self):
        return f"<IncomeAdjustDetail: {self.order_no}>"

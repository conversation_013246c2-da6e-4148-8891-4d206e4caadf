import io
from datetime import datetime

import pandas as pd
from django.db.models import Case
from django.db.models import F
from django.db.models import OuterRef
from django.db.models import Subquery
from django.db.models import Value
from django.db.models import When
from django.db.models.query import QuerySet
from django.db.models.query import RawQuerySet
from django.http import HttpResponse
from django.utils.encoding import escape_uri_path

from income import const
from income.contract.models import ContractInfo
from income.customer.models import CustomerInfo
from income.order.models import IncomeOrderInfo
from income.utils.common import partial_register

from .models import IncomeChargeDetail


def export_to_excel(queryset: RawQuerySet):
    """导出出账明细数据为Excel文件

    Args:
        queryset (RawQuerySet): 筛选后的数据

    Returns:
        _type_: HttpResponse
    """
    data_list = [row.__dict__ for row in queryset]
    drop_labels = [
        "_state",
        "id",
        "income_adjust_id",
        "updated_at",
        "bill_used_nums",
        "group_approve_state",
    ]
    # 创建DataFrame
    if not data_list:
        # 如果没有数据,创建空的DataFrame
        charge_df = pd.DataFrame(
            columns=list(const.FIELD_MAPPING.keys()) + drop_labels,
        )
    else:
        charge_df = pd.DataFrame(data_list)
    charge_df = charge_df.drop(
        labels=[
            "_state",
            "id",
            "income_adjust_id",
            "updated_at",
            "bill_used_nums",
            "group_approve_state",
        ],
        axis=1,
    )
    charge_df["charge_type"] = charge_df["charge_type"].map(
        dict(const.ChargeType.choices),
    )
    charge_df = charge_df.rename(columns=const.FIELD_MAPPING)
    # 生成Excel文件
    bio = io.BytesIO()
    with pd.ExcelWriter(bio, engine="openpyxl") as writer:
        charge_df.to_excel(writer, sheet_name="出账明细", index=False)
    bio.seek(0)

    # 生成文件名
    filename = f"出账明细_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
    # 创建HTTP响应
    response = HttpResponse(
        content=bio.getvalue(),
        content_type="application/vnd.ms-excel",
    )
    response["Content-Disposition"] = (
        f'attachment; filename="{escape_uri_path(filename)}"'
    )
    return response


def adjust_export_to_excel(queryset: QuerySet):
    """导出调账明细数据为Excel文件"""

    # 创建DataFrame
    if not queryset:
        # 如果没有数据,创建空的DataFrame
        charge_df = pd.DataFrame(columns=list(const.ADJUST_FIELD_MAPPING.keys()))
    else:
        charge_df = pd.DataFrame(queryset)
    charge_df["adjust_type"] = charge_df["adjust_type"].map(
        dict(const.AdjustType.choices),
    )
    charge_df["state"] = charge_df["state"].map(dict(const.AdjustState.choices))
    charge_df = charge_df.rename(columns=const.ADJUST_FIELD_MAPPING)
    # 生成Excel文件
    bio = io.BytesIO()
    with pd.ExcelWriter(bio, engine="openpyxl") as writer:
        charge_df.to_excel(writer, sheet_name="调账明细", index=False)
    bio.seek(0)

    # 生成文件名
    filename = f"调账明细_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
    # 创建HTTP响应
    response = HttpResponse(
        content=bio.getvalue(),
        content_type="application/vnd.ms-excel",
    )
    response["Content-Disposition"] = (
        f'attachment; filename="{escape_uri_path(filename)}"'
    )
    return response


def add_extra_field(query_data_list: list):
    """添加额外字段

    :param query_data_list:
    :return:
    """
    customer_nums = [query_data["customer_num"] for query_data in query_data_list]
    customer_qs = CustomerInfo.objects.filter(
        customer_num__in=set(customer_nums),
    ).values_list("customer_num", "customer_name")
    customer_map = dict(customer_qs)
    for query_data in query_data_list:
        query_data["customer_name"] = customer_map.get(query_data.pop("customer_num"))
    return query_data_list


CREATE_CHARGE_DETAIL_MAP = {}

_register = partial_register(CREATE_CHARGE_DETAIL_MAP)


@_register(const.AdjustType.ACCRUAL)
def accrual_charge_detail(kwargs: dict):
    """创建权责信息

    :param kwargs: 创建权责信息需要的参数
    :type kwargs: dict
    :return: None
    """
    # 获取当前调整的权责明细
    info = IncomeChargeDetail.objects.values(
        "batch_no",
        "order_no",
        "sub_order_no",
        "order_charge_begin",
        "order_charge_end",
        "tax_type",
        "tax",
        "fee_type",
        "pay_type",
        "currency_type",
        "income_type",
        "speed",
        "charge_explain",
        "a_info",
        "product_main_category",
        "product_sub_category",
        "order_type",
        "bill_template",
        "contract_num",
        "current_attr",
        "customer_num",
        "account_seq",
        "charge_user",
    ).get(pk=kwargs["charge_detail_id"])
    info["charge_type"] = const.ChargeType.ADJUSTMENT
    info["fee_amount"] = kwargs["adjust_amount"]
    info["pay_amount"] = 0.00
    info["unpay_amount"] = kwargs["adjust_amount"]
    info["charge_month"] = kwargs["charge_month"]
    info["income_adjust_id"] = kwargs["adjust_id"]
    IncomeChargeDetail.objects.create(**info)


@_register(const.AdjustType.NON_ACCRUAL)
def non_accrual_charge_detail(kwargs: dict):
    """创建无权责信息

    :param kwargs: 创建无权责信息需要的参数
    :type kwargs: dict
    :return: None
    """
    info = IncomeOrderInfo.objects.annotate(
        customer_num=Subquery(
            ContractInfo.objects.filter(
                contract_num=OuterRef("contract_num"),
            ).values("main_customer_num")[:1],
        ),
        current_attr=Case(
            When(
                order_start_year=datetime.now().year,
                then=Value("新增"),
            ),
            default=Value("递延"),
        ),
        order_charge_begin=F("reality_bill_start_date"),
        order_charge_end=F("reality_bill_end_date"),
        fee_type=F("pay_cycle"),
        order_no=F("order_num"),
        sub_order_no=F("total_num"),
        tax=F("tax_rate"),
        charge_user=F("create_user"),
    ).values(
        "order_no",
        "sub_order_no",
        "order_charge_begin",
        "order_charge_end",
        "fee_type",
        "pay_type",
        "income_type",
        "charge_explain",
        "a_info",
        "product_main_category",
        "product_sub_category",
        "order_type",
        "contract_num",
        "customer_num",
        "account_seq",
        "tax",
        "tax_type",
        "currency_type",
        "charge_user",
        "current_attr",
    ).get(total_num=kwargs["total_num"])
    info["charge_type"] = const.ChargeType.ADJUSTMENT
    info["fee_amount"] = kwargs["adjust_amount"]
    info["pay_amount"] = 0.00
    info["unpay_amount"] = kwargs["adjust_amount"]
    info["charge_month"] = kwargs["charge_month"]
    info["income_adjust_id"] = kwargs["adjust_id"]
    IncomeChargeDetail.objects.create(**info)


def income_adjust_add_extra_field(query_data_list: list):
    """添加额外字段

    :param query_data_list:
    [
        {
            "id",
            "order_num",
            "total_num",
            "bill_status",
            "income_type",
            "tax_type",
            "contract_num"
        }
    ]
    :return:
    """
    contract_nums = [query_data["contract_num"] for query_data in query_data_list]
    contract_qs = ContractInfo.objects.filter(
        contract_num__in=set(contract_nums),
    ).values_list("contract_num", "main_customer_num")
    contract_map = dict(contract_qs)
    for query_data in query_data_list:
        query_data["main_customer_num"] = contract_map.get(
            query_data.pop("contract_num"),
        )
    return query_data_list

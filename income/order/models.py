from django.db import models

from income import const


class IncomeOrderInfo(models.Model):
    """收入订单信息"""

    order_type = models.CharField(
        max_length=20,
        choices=const.OrderType.choices,
        db_comment="订单类型: 客户正式、客户测试、自用、注销",
    )
    order_class = models.IntegerField(
        blank=True,
        null=True,
        choices=const.OrderClass.choices,
        db_comment="订单种类: 0-普通;1-语音订单;2-流量订单",
    )
    service_status = models.CharField(
        max_length=50,
        choices=const.OrderServiceStatus.choices,
        db_comment="订单服务状态",
    )
    bill_status = models.CharField(
        max_length=50,
        choices=const.OrderBillStatus.choices,
        db_comment="订单计费状态",
    )
    job_status = models.CharField(
        max_length=20,
        blank=True,
        null=True,
        choices=const.JobStatus.choices,
        db_comment="派工状态: 未派工/已派工/派工撤销",
    )
    order_contract_period = models.IntegerField(
        db_comment="订单合约期,月数",
        default=1,
    )
    order_start_year = models.CharField(
        max_length=10,
        db_comment="订单起始年",
        error_messages={
            "required": "订单起始年为必填项",
        },
    )
    order_remark = models.TextField(blank=True, null=True, db_comment="订单备注")
    business_product_type = models.CharField(
        max_length=20,
        db_comment="选择业务产品类型: 是某串业务产品字母,比如MPLS",
    )

    order_num = models.CharField(
        max_length=50,
        db_comment="业务产品字母-4位年2位月6位递增数字",
    )
    sub_order_num = models.CharField(max_length=1, db_comment="A-Z")
    charge_order_num = models.CharField(
        max_length=10,
        db_comment="自动递增数字, 起始为BG0",
    )
    total_num = models.CharField(
        max_length=50,
        db_comment="合成编号:订单编号-子订单序号-BG变更序号",
        unique=True,
        error_messages={
            "unique": "合成编号重复,请重新输入相关字段",
        },
    )

    service_type = models.CharField(
        max_length=10,
        choices=const.ServiceType.choices,
        db_comment="服务类型: 新增/变更/续约",
    )
    pre_order_total_num = models.CharField(
        max_length=50,
        blank=True,
        null=True,
        db_comment="前序订单合成编号",
    )
    income_type = models.CharField(
        max_length=40,
        db_comment="收入分类: static_data表type=income_type",
    )
    product_main_category = models.CharField(
        max_length=40,
        blank=True,
        null=True,
        db_comment="产品主类",
    )
    product_sub_category = models.CharField(
        max_length=40,
        db_collation="utf8mb4_bin",
        blank=True,
        null=True,
        db_comment="产品次类",
    )
    product_scheme = models.TextField(blank=True, null=True, db_comment="产品开通方案")
    product_after_sale = models.TextField(
        blank=True,
        null=True,
        db_comment="售后服务要求",
    )
    product_upload_file = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        db_comment="需求、开通及售后要求附件",
    )
    pay_cycle = models.CharField(
        max_length=10,
        blank=True,
        null=True,
        choices=const.PayCycle.choices,
        db_comment="付费周期: 月/季度/年",
    )
    pay_type = models.CharField(
        max_length=10,
        blank=True,
        null=True,
        choices=const.PayType.choices,
        db_comment="付费方式: 预付/后付/当月付",
    )
    account_seq = models.CharField(
        max_length=50,
        blank=True,
        null=True,
        db_comment="分账序号",
    )

    a_info = models.CharField(
        max_length=500,
        blank=True,
        null=True,
        db_comment="a端信息",
    )
    a_address = models.CharField(
        max_length=200,
        blank=True,
        null=True,
        db_comment="a端地址",
    )
    z_info = models.CharField(
        max_length=500,
        blank=True,
        null=True,
        db_comment="z端信息",
    )
    z_address = models.CharField(
        max_length=200,
        blank=True,
        null=True,
        db_comment="z段地址",
    )

    partner_name = models.CharField(
        max_length=50,
        blank=True,
        null=True,
        db_comment="合作商名称: 比如: NTT、PCCW",
    )
    partner_po_num = models.CharField(
        max_length=50,
        blank=True,
        null=True,
        db_comment="合作商PO编号",
    )
    new_required_finished_date = models.DateField(
        blank=True,
        null=True,
        db_comment="新装要求完工日",
    )
    reality_bill_start_date = models.DateField(
        blank=True,
        null=True,
        db_comment="实际计费始日",
    )
    new_build_start_time = models.DateTimeField(
        blank=True,
        null=True,
        db_comment="新装实施开始时间:实际开始实施时间",
    )
    new_build_finished_time = models.DateTimeField(
        blank=True,
        null=True,
        db_comment="新装实施报完工时间:实施报完工",
    )
    new_build_bill_time = models.DateTimeField(
        blank=True,
        null=True,
        db_comment="新装计费报完工时间:收入报完工",
    )
    new_build_charge_time = models.DateTimeField(
        blank=True,
        null=True,
        db_comment="新装账务复核时间:账务确认时间",
    )
    new_build_support_time = models.DateTimeField(
        blank=True,
        null=True,
        db_comment="新装维护确认时间: 客服确认",
    )
    remove_required_finished_date = models.DateField(
        blank=True,
        null=True,
        db_comment="拆机要求完工日",
    )
    reality_bill_end_date = models.DateField(
        blank=True,
        null=True,
        db_comment="实际计费终日",
    )
    remove_build_start_time = models.DateTimeField(
        blank=True,
        null=True,
        db_comment="拆机实施开始时间",
    )
    remove_build_finished_time = models.DateTimeField(
        blank=True,
        null=True,
        db_comment="拆机实施报完工时间",
    )
    remove_build_bill_time = models.DateTimeField(
        blank=True,
        null=True,
        db_comment="拆机计费报完工时间",
    )
    remove_build_charge_time = models.DateTimeField(
        blank=True,
        null=True,
        db_comment="拆机账务复核时间",
    )
    remove_build_support_time = models.DateTimeField(
        blank=True,
        null=True,
        db_comment="拆机维护确认时间",
    )
    finished_remark = models.TextField(blank=True, null=True, db_comment="完工备注")
    finished_file = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        db_comment="完工附件",
    )
    group_approve_state = models.CharField(
        max_length=10,
        blank=True,
        null=True,
        db_comment="集团审批状态",
    )
    income_fee_package_id = models.IntegerField(db_comment="套餐")
    once_fee = models.IntegerField(blank=True, null=True, db_comment="一次性费用")
    cycle_fee = models.IntegerField(blank=True, null=True, db_comment="周期费用")
    tax_rate = models.IntegerField(blank=True, null=True, db_comment="税率")
    tax_type = models.CharField(max_length=50, db_comment="税率类型")
    currency_type = models.CharField(max_length=20, db_comment="币种")
    charge_explain = models.CharField(
        max_length=255,
        blank=True,
        null=True,
        db_comment="资费说明",
    )
    charge_remark = models.CharField(
        max_length=255,
        blank=True,
        null=True,
        db_comment="资费备注",
    )

    contract_num = models.CharField(max_length=50, db_comment="合同编号")
    customer_num = models.CharField(max_length=50, db_comment="客户编号")
    create_user = models.CharField(max_length=50, db_comment="创建者")

    created_at = models.DateTimeField(db_comment="创建时间", auto_now_add=True)
    updated_at = models.DateTimeField(db_comment="更新时间", auto_now=True)

    class Meta:
        managed = False
        db_table = "income_order_info"
        ordering = ["-created_at"]

    def __str__(self):
        return f"<IncomeOrderInfo: {self.total_num}>"

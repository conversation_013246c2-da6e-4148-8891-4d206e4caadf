from datetime import datetime

from django.db.models import Max
from rest_framework import serializers

from income import const
from income import message
from income.fee.models import IncomeFeePackage

from .models import IncomeOrderInfo


class OrderInfoSerializer(serializers.ModelSerializer):
    class Meta:
        model = IncomeOrderInfo
        exclude = ("order_num", "charge_order_num", "updated_at")
        read_only_fields = const.ORDER_READ_ONLY_FIELDS
        write_only_fields = ("sub_order_num",)

    @staticmethod
    def generate_order_num():
        current_time = datetime.now().strftime("%Y%m")
        max_order_num = IncomeOrderInfo.objects.filter(
            order_num__startswith=current_time,
        ).aggregate(number_max=Max("order_num"))
        max_num = max_order_num.get("number_max")
        return (
            f"{current_time}{(int(max_num[6:]) + 1):0>6}"
            if max_num
            else f"{current_time}000001"
        )

    @staticmethod
    def generate_charge_order_num(
        business_product_type: str,
        order_num: str,
        sub_order_num: str,
    ):
        """生成递增的charge_order_num

        :param business_product_type: 业务产品类型
        :param order_num: 订单编号
        :param sub_order_num: 子订单编号
        :return: str
        """
        prefix = "BG"
        max_charge_order_num = IncomeOrderInfo.objects.filter(
            business_product_type=business_product_type,
            order_num=order_num,
            sub_order_num=sub_order_num,
            charge_order_num__startswith=prefix,
        ).aggregate(number_max=Max("charge_order_num"))
        max_num = max_charge_order_num.get("number_max")
        return f"{prefix}{int(max_num[2:]) + 1}" if max_num else f"{prefix}0"

    def custom_validate_total_num(self, total_num: str):
        """校验合成编号是否存在"""

        if self.Meta.model.objects.filter(total_num=total_num).exists():
            raise serializers.ValidationError(
                {"total_num": message.ORDER_TOTAL_NUM_EXIStS},
            )

    def validate_income_fee_package_id(self, value):
        """校验套餐是否存在"""

        if not IncomeFeePackage.objects.filter(id=value).exists():
            raise serializers.ValidationError(message.FEE_PACKAGE_NOT_FOUND)
        return value

    def validate(self, attrs):
        # 判断如果是修改,
        if self.instance:
            # 并且子订单编号进行过变动
            if self.instance.sub_order_num != attrs["sub_order_num"]:
                # 仅修改charge_order_num
                charge_order_num = self.generate_charge_order_num(
                    attrs["business_product_type"],
                    self.instance.order_num,
                    attrs["sub_order_num"],
                )
                attrs["charge_order_num"] = charge_order_num
                # 变更合成编号
                total_num = f"""{attrs["business_product_type"]}-{self.instance.order_num}-{attrs["sub_order_num"]}-{charge_order_num}"""  # noqa: E501
                self.custom_validate_total_num(total_num)
                attrs["total_num"] = total_num
            return attrs
        # 新建操作时
        order_num = self.generate_order_num()
        attrs["order_num"] = order_num
        charge_order_num = self.generate_charge_order_num(
            attrs["business_product_type"],
            order_num,
            attrs["sub_order_num"],
        )
        attrs["charge_order_num"] = charge_order_num
        total_num = f"""{attrs["business_product_type"]}-{order_num}-{attrs["sub_order_num"]}-{charge_order_num}"""  # noqa: E501
        self.custom_validate_total_num(total_num)
        attrs["total_num"] = total_num
        return attrs

    def create(self, validated_data):
        create_user = self.context["request"].user.username
        validated_data["create_user"] = create_user
        validated_data["service_status"] = const.OrderServiceStatus.DRAFT
        validated_data["bill_status"] = const.OrderBillStatus.INCOMPLETE
        return self.Meta.model.objects.create(**validated_data)

    def update(self, instance, validated_data):
        request = self.context["request"]
        validated_data["create_user"] = request.user.username
        for k, v in validated_data.items():
            setattr(instance, k, v)
        instance.save()
        return instance


class OrderChangeServiceStatusSerializer(serializers.ModelSerializer):
    """更新服务状态"""

    service_status = serializers.ChoiceField(
        choices=const.OrderServiceStatus.choices,
        help_text="变更的服务状态",
    )

    class Meta:
        model = IncomeOrderInfo
        fields = (
            "service_status",
            "finished_remark",
            "order_remark",
            "new_build_start_time",
            "job_status",
            "product_scheme",
            "remove_required_finished_date",
            "remove_build_start_time",
        )


class OrderChangeBillStatusSerializer(serializers.ModelSerializer):
    """更新计费状态"""

    bill_status = serializers.ChoiceField(
        choices=const.OrderBillStatus.choices,
        help_text="变更的计费状态",
    )

    class Meta:
        model = IncomeOrderInfo
        fields = (
            "bill_status",
            "account_seq",
            "reality_bill_start_date",
            "order_start_year",
            "order_remark",
            "finished_remark",
            "reality_bill_end_date",
        )


class ChangeOrderSerializer(serializers.ModelSerializer):
    """变更订单信息"""

    class Meta:
        model = IncomeOrderInfo
        exclude = ("updated_at",)
        read_only_fields = (
            *const.ORDER_READ_ONLY_FIELDS,
            *(
                "sub_order_num",
                "charge_order_num",
                "order_num",
            ),
        )

    def validate(self, attrs):
        # 从context中获取parent_order
        parent_order = self.context.get("parent_order")

        # 变更订单时将charge_order_num向上加1, BG0->BG1
        # 保留原订单的order_num和sub_order_num
        attrs["order_num"] = parent_order.order_num
        attrs["sub_order_num"] = parent_order.sub_order_num

        # 从BG0获取数字部分, 转为整数后加1
        current_charge_num = parent_order.charge_order_num
        prefix = "BG"
        num_part = int(current_charge_num[2:])
        new_charge_order_num = f"{prefix}{num_part + 1}"
        attrs["charge_order_num"] = new_charge_order_num

        # 更新total_num
        total_num = f"""{
            attrs.get("business_product_type", parent_order.business_product_type)
        }-{parent_order.order_num}-{parent_order.sub_order_num}-{
            new_charge_order_num
        }"""
        self.custom_validate_total_num(total_num)
        attrs["total_num"] = total_num

        attrs["service_status"] = const.OrderServiceStatus.DRAFT
        attrs["bill_status"] = const.OrderBillStatus.INCOMPLETE
        attrs["service_type"] = const.ServiceType.CHANGE
        # 设置原订单为前序订单
        attrs["pre_order_total_num"] = parent_order.total_num
        return attrs

    def custom_validate_total_num(self, total_num: str):
        """校验合成编号是否存在"""
        if self.Meta.model.objects.filter(total_num=total_num).exists():
            raise serializers.ValidationError(
                {"total_num": message.ORDER_TOTAL_NUM_EXIStS},
            )

    def create(self, validated_data):
        create_user = self.context["request"].user.username
        validated_data["create_user"] = create_user
        return self.Meta.model.objects.create(**validated_data)


class NewSubOrderSerializer(serializers.ModelSerializer):
    """新增子订单"""

    class Meta:
        model = IncomeOrderInfo
        exclude = ("updated_at",)
        read_only_fields = (
            *const.ORDER_READ_ONLY_FIELDS,
            *(
                "charge_order_num",
                "order_num",
            ),
        )

    def validate(self, attrs):
        # 从context中获取parent_order
        parent_order = self.context.get("parent_order")
        # 保留原订单的order_num
        attrs["order_num"] = parent_order.order_num

        # charge_order_num设置为默认值BG0
        attrs["charge_order_num"] = "BG0"

        # 更新total_num
        total_num = f"""{
            attrs.get("business_product_type", parent_order.business_product_type)
        }-{parent_order.order_num}-{attrs["sub_order_num"]}-BG0"""
        self.custom_validate_total_num(total_num)
        attrs["total_num"] = total_num

        # 设置默认状态
        attrs["service_status"] = const.OrderServiceStatus.DRAFT
        attrs["bill_status"] = const.OrderBillStatus.INCOMPLETE

        # 设置服务类型和原订单为前序订单
        attrs["service_type"] = const.ServiceType.NEW
        attrs["pre_order_total_num"] = parent_order.total_num

        return attrs

    def custom_validate_total_num(self, total_num: str):
        """校验合成编号是否存在"""
        if self.Meta.model.objects.filter(total_num=total_num).exists():
            raise serializers.ValidationError(
                {"total_num": message.ORDER_TOTAL_NUM_EXIStS},
            )

    def create(self, validated_data):
        create_user = self.context["request"].user.username
        validated_data["create_user"] = create_user
        # 创建新的子订单记录
        return self.Meta.model.objects.create(**validated_data)


class RenewalOrderSerializer(serializers.ModelSerializer):
    """续约订单序列化器"""

    class Meta:
        model = IncomeOrderInfo
        exclude = ("updated_at", "order_num", "charge_order_num")
        read_only_fields = (*const.ORDER_READ_ONLY_FIELDS,)
        write_only_fields = ("sub_order_num",)

    def validate(self, attrs):
        # 从context中获取parent_order
        parent_order = self.context.get("parent_order")

        # 续约订单需要重新生成order_num
        order_num = OrderInfoSerializer.generate_order_num()
        attrs["order_num"] = order_num

        # 生成charge_order_num
        business_product_type = attrs["business_product_type"]
        charge_order_num = OrderInfoSerializer.generate_charge_order_num(
            business_product_type,
            order_num,
            attrs["sub_order_num"],
        )
        attrs["charge_order_num"] = charge_order_num

        # 拼接total_num
        total_num = f"""{business_product_type}-{order_num}-{attrs["sub_order_num"]}-{charge_order_num}"""  # noqa: E501
        self.custom_validate_total_num(total_num)
        attrs["total_num"] = total_num

        # 设置默认状态
        attrs["service_status"] = const.OrderServiceStatus.DRAFT
        attrs["bill_status"] = const.OrderBillStatus.INCOMPLETE
        # 设置服务类型为续约,并设置前序订单
        attrs["service_type"] = const.ServiceType.RENEWAL
        attrs["pre_order_total_num"] = parent_order.total_num

        return attrs

    def custom_validate_total_num(self, total_num: str):
        """校验合成编号是否存在"""
        if self.Meta.model.objects.filter(total_num=total_num).exists():
            raise serializers.ValidationError(
                {"total_num": message.ORDER_TOTAL_NUM_EXIStS},
            )

    def create(self, validated_data):
        create_user = self.context["request"].user.username
        validated_data["create_user"] = create_user
        # 创建续约订单记录
        return self.Meta.model.objects.create(**validated_data)

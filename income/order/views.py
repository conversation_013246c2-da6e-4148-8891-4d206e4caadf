from drf_spectacular.utils import extend_schema
from drf_spectacular.utils import extend_schema_view
from rest_framework import mixins
from rest_framework.decorators import action
from rest_framework.exceptions import ParseError
from rest_framework.response import Response

from income import const
from income import message
from income.contrib.drf.filters import SearchFilter
from income.contrib.drf.views import GenericViewSet
from income.contrib.drf.views import UpdateModelMixin
from income.permissions import IsAuthenticated
from income.permissions import RoleMenuPermission

from .filters import ContractNumberFilter
from .models import IncomeOrderInfo
from .serializers import ChangeOrderSerializer
from .serializers import NewSubOrderSerializer
from .serializers import OrderChangeBillStatusSerializer
from .serializers import OrderChangeServiceStatusSerializer
from .serializers import OrderInfoSerializer
from .serializers import RenewalOrderSerializer
from .utils import order_info_add_extra_field


@extend_schema_view(
    list=extend_schema(summary="获取收入订单信息"),
    create=extend_schema(summary="创建收入订单信息"),
    update=extend_schema(summary="更新ID对应的收入订单信息"),
    retrieve=extend_schema(summary="根据ID获取收入订单的详细信息"),
    change_service_status=extend_schema(summary="更新服务状态"),
    change_bill_status=extend_schema(summary="更新计费状态"),
    change_order=extend_schema(summary="变更订单信息"),
    new_sub_order=extend_schema(summary="新增子订单"),
    renewal_order=extend_schema(summary="续约订单"),
)
@extend_schema(tags=["order-info"])
class OrderInfoViewSet(
    GenericViewSet,
    mixins.ListModelMixin,
    mixins.CreateModelMixin,
    mixins.RetrieveModelMixin,
    UpdateModelMixin,
):
    """订单信息"""

    search_fields = ["total_num", "create_user"]
    search_contains = True

    serializer_class = OrderInfoSerializer
    serializers = {
        "change_service_status": OrderChangeServiceStatusSerializer,
        "change_bill_status": OrderChangeBillStatusSerializer,
        "change_order": ChangeOrderSerializer,
        "new_sub_order": NewSubOrderSerializer,
        "renewal_order": RenewalOrderSerializer,
    }
    permission_classes = (IsAuthenticated, RoleMenuPermission)
    filter_backends = (SearchFilter, ContractNumberFilter)

    identify = const.MenuIdentify.INCOME_ORDER

    def get_queryset(self):
        # 查询id小200的订单信息
        return IncomeOrderInfo.objects.all()

    def get_serializer_class(self):
        return self.serializers.get(self.action, self.serializer_class)

    def update(self, request, *args, **kwargs):
        instance = self.get_object(for_update=True)
        if instance.service_status != const.OrderServiceStatus.DRAFT:
            raise ParseError(message.ORDER_SERVICE_STATUS_NOT_MODIFY)
        serializer = self.get_serializer(instance, data=request.data, partial=False)
        serializer.is_valid(raise_exception=True)
        self.perform_update(serializer)
        return Response(serializer.data)

    def list(self, request, *args, **kwargs):
        fields = [field.name for field in IncomeOrderInfo._meta.fields]  # noqa: SLF001
        queryset = self.filter_queryset(
            self.get_queryset(),
        ).values(*fields)
        page = self.paginate_queryset(queryset)
        if page is not None:
            data = order_info_add_extra_field(page)
            return self.get_paginated_response(data)
        data = order_info_add_extra_field(queryset)
        return Response(data)

    @extend_schema(
        tags=["order-info"],
        request=OrderChangeServiceStatusSerializer,
        summary="更新服务状态",
        description="更新服务状态",
    )
    @action(
        detail=True,
        methods=["post"],
        url_path="change-service-status",
    )
    def change_service_status(self, request, *args, **kwargs):
        """更新服务状态"""
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data)
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response(serializer.data)

    @extend_schema(
        tags=["order-info"],
        request=OrderChangeBillStatusSerializer,
        summary="更新计费状态",
        description="更新计费状态",
    )
    @action(
        detail=True,
        methods=["post"],
        url_path="change-billing-status",
    )
    def change_bill_status(self, request, *args, **kwargs):
        """更新计费状态"""
        instance = self.get_object()
        # 当订单的服务状态在以下范围时不可进行计费审核
        if instance.service_status in [
            const.OrderServiceStatus.DRAFT,
            const.OrderServiceStatus.ORDER_REVIEW,
            const.OrderServiceStatus.DISPATCHED,
            const.OrderServiceStatus.DECOM_ROLLBACK,
            const.OrderServiceStatus.DECOM_ORDER_REVIEW,
            const.OrderServiceStatus.DECOM_DISPATCHED,
            const.OrderServiceStatus.CHG_DECOM_ROLLBACK,
            const.OrderServiceStatus.CHG_DECOM_ORDER_REVIEW,
            const.OrderServiceStatus.CHG_DECOM_DISPATCHED,
        ]:
            raise ParseError(message.ORDER_SERVICE_STATUS_NOT_CHANGE_BILL_STATUS)
        serializer = self.get_serializer(instance, data=request.data)
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response(serializer.data)

    @extend_schema(
        tags=["order-info"],
        request=ChangeOrderSerializer,
        summary="变更订单信息",
        description="变更订单信息, 将charge_order_num向上加1, BG0->BG1",
    )
    @action(
        detail=True,
        methods=["post"],
        url_path="change-order",
    )
    def change_order(self, request, *args, **kwargs):
        """变更订单信息"""
        parent_order = self.get_object()
        serializer = self.get_serializer(
            data=request.data,
            context={"request": request, "parent_order": parent_order},
        )
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response(serializer.data)

    @extend_schema(
        tags=["order-info"],
        request=NewSubOrderSerializer,
        summary="新增子订单",
        description="新增子订单, 将sub_order_num的值向上变动一位, charge_order_num设置为默认值BG0",  # noqa: E501
    )
    @action(
        detail=True,
        methods=["post"],
        url_path="new-sub-order",
    )
    def new_sub_order(self, request, *args, **kwargs):
        """新增子订单"""
        parent_order = self.get_object()
        serializer = self.get_serializer(
            data=request.data,
            context={"request": request, "parent_order": parent_order},
        )
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response(serializer.data)

    @extend_schema(
        tags=["order-info"],
        request=RenewalOrderSerializer,
        summary="续约订单",
        description="续约订单,重新生成order_num和total_num,设置服务类型为续约",
    )
    @action(
        detail=True,
        methods=["post"],
        url_path="renewal-order",
    )
    def renewal_order(self, request, *args, **kwargs):
        """续约订单"""
        parent_order = self.get_object()
        serializer = self.get_serializer(
            data=request.data,
            context={"request": request, "parent_order": parent_order},
        )
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response(serializer.data)

from income.contract.models import ContractInfo


def order_info_add_extra_field(query_data_list: list):

    contract_num_list = [
       query_data["contract_num"] for query_data in query_data_list
    ]

    contract_qs = ContractInfo.objects.filter(
        contract_num__in=set(contract_num_list),
    ).values_list("contract_num", "contract_title")
    contract_map = dict(contract_qs)
    for query_data in query_data_list:
        query_data["contract_title"] = contract_map.get(query_data["contract_num"])
        query_data["created_at"] = query_data["created_at"].strftime("%s")
    return query_data_list

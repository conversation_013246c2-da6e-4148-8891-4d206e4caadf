# Generated by Django 5.0.13 on 2025-04-07 05:32

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='OrderAnalysis',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('business_owner', models.Char<PERSON><PERSON>(blank=True, max_length=64, null=True)),
                ('business_line', models.Char<PERSON>ield(blank=True, max_length=64, null=True, verbose_name='产品大类')),
                ('business_source', models.Char<PERSON><PERSON>(blank=True, max_length=64, null=True)),
                ('business_region', models.Char<PERSON>ield(blank=True, max_length=64, null=True)),
                ('business_class', models.Char<PERSON>ield(blank=True, max_length=64, null=True)),
                ('business_type', models.Char<PERSON>ield(blank=True, max_length=64, null=True)),
                ('business_sub_type', models.<PERSON><PERSON><PERSON><PERSON>(blank=True, max_length=64, null=True)),
                ('business_income_type', models.CharField(blank=True, max_length=64, null=True)),
                ('business_remark', models.CharField(blank=True, max_length=1024, null=True)),
                ('finance_newold_type', models.CharField(blank=True, max_length=64, null=True)),
                ('finance_income_type', models.CharField(blank=True, max_length=64, null=True, verbose_name='财务科目')),
                ('business_project', models.CharField(blank=True, max_length=128, null=True)),
                ('creator', models.CharField(max_length=32, verbose_name='创建用户')),
                ('order_id', models.PositiveIntegerField(db_index=True, verbose_name='收入订单ID')),
                ('created', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '订单信息分析表',
                'db_table': 'order_analysis',
            },
        ),
        migrations.CreateModel(
            name='OrderInfo',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('create_user', models.CharField(blank=True, max_length=32, null=True)),
                ('order_time', models.DateTimeField(verbose_name='订单日期')),
                ('service_status', models.CharField(max_length=32, verbose_name='服务状态')),
                ('bill_status', models.CharField(max_length=32, verbose_name='计费状态')),
                ('order_type', models.CharField(max_length=32)),
                ('job_status', models.CharField(max_length=32)),
                ('contract_num', models.CharField(db_index=True, max_length=32, verbose_name='合同编号-系统编号')),
                ('business_product_type', models.CharField(max_length=32)),
                ('order_num', models.CharField(db_index=True, max_length=64, verbose_name='订单号-总订单')),
                ('sub_order_num', models.CharField(max_length=32)),
                ('change_order_num', models.CharField(max_length=32)),
                ('total_num', models.CharField(max_length=64, unique=True, verbose_name='订单号-子订单')),
                ('order_contract_months', models.IntegerField(blank=True, null=True)),
                ('order_remark', models.CharField(blank=True, max_length=1024, null=True)),
                ('service_type', models.CharField(max_length=32, verbose_name='服务类型')),
                ('product_type', models.CharField(max_length=32, verbose_name='产品类型')),
                ('product_requirement', models.CharField(blank=True, max_length=1024, null=True)),
                ('product_scheme', models.CharField(blank=True, max_length=1024, null=True)),
                ('product_after_sale', models.CharField(blank=True, max_length=1024, null=True)),
                ('product_upload_file', models.CharField(blank=True, max_length=1024, null=True)),
                ('charge_speed', models.CharField(blank=True, max_length=64, null=True)),
                ('currency_type', models.CharField(max_length=32, verbose_name='货币类型')),
                ('charge_tax_rate', models.DecimalField(blank=True, decimal_places=4, max_digits=14, null=True, verbose_name='税率')),
                ('charge_fee_type', models.CharField(blank=True, max_length=32, null=True)),
                ('charge_month_money', models.DecimalField(blank=True, decimal_places=4, max_digits=14, null=True, verbose_name='周期费用')),
                ('charge_fee_cycle', models.CharField(max_length=32, verbose_name='计费周期,月/季度/年')),
                ('charge_pay_type', models.CharField(max_length=32, verbose_name='付费类型')),
                ('charge_flow_price', models.DecimalField(blank=True, decimal_places=4, max_digits=14, null=True)),
                ('charge_flow_bill_explain', models.CharField(blank=True, max_length=1024, null=True)),
                ('charge_bill_rule', models.CharField(blank=True, max_length=1024, null=True)),
                ('charge_explain', models.CharField(blank=True, max_length=1024, null=True, verbose_name='资费说明-用户端名称')),
                ('charge_remark', models.CharField(blank=True, max_length=1024, null=True)),
                ('bill_way', models.CharField(blank=True, max_length=32, null=True)),
                ('bill_num', models.CharField(blank=True, max_length=32, null=True)),
                ('bill_remark', models.CharField(blank=True, max_length=512, null=True)),
                ('a_info', models.CharField(blank=True, max_length=512, null=True)),
                ('a_address', models.CharField(blank=True, max_length=512, null=True)),
                ('z_info', models.CharField(blank=True, max_length=512, null=True)),
                ('z_address', models.CharField(blank=True, max_length=512, null=True)),
                ('end_user_info', models.CharField(blank=True, max_length=128, null=True)),
                ('partner_info', models.CharField(blank=True, max_length=128, null=True)),
                ('partner_num', models.CharField(blank=True, max_length=64, null=True)),
                ('new_required_finished_day', models.DateField(blank=True, null=True)),
                ('required_bill_start_day', models.DateField(blank=True, null=True)),
                ('reality_bill_start_day', models.DateField(blank=True, null=True, verbose_name='计费始日')),
                ('new_build_start_time', models.DateTimeField(blank=True, null=True)),
                ('new_build_finished_time', models.DateTimeField(blank=True, null=True)),
                ('new_build_bill_time', models.DateTimeField(blank=True, null=True, verbose_name='新装计费报完工时间')),
                ('new_build_charge_time', models.DateTimeField(blank=True, null=True, verbose_name='新装账务复核时间')),
                ('new_build_support_time', models.DateTimeField(blank=True, null=True)),
                ('remove_required_finished_day', models.DateField(blank=True, null=True)),
                ('required_bill_end_day', models.DateField(blank=True, null=True)),
                ('reality_bill_end_day', models.DateField(blank=True, null=True, verbose_name='计费终日')),
                ('remove_build_start_time', models.DateTimeField(blank=True, null=True)),
                ('remove_build_finished_time', models.DateTimeField(blank=True, null=True)),
                ('remove_build_bill_time', models.DateTimeField(blank=True, null=True, verbose_name='拆机计费报完工时间')),
                ('remove_build_charge_time', models.DateTimeField(blank=True, null=True, verbose_name='拆机账务复核时间')),
                ('remove_build_support_time', models.DateTimeField(blank=True, null=True)),
                ('finished_remark', models.CharField(blank=True, max_length=1024, null=True)),
                ('finished_upload_file', models.CharField(blank=True, max_length=1024, null=True)),
                ('business_main', models.CharField(blank=True, max_length=128, null=True)),
                ('project_num', models.CharField(blank=True, max_length=32, null=True)),
                ('support_upload_file', models.CharField(blank=True, max_length=1024, null=True)),
                ('order_start_year', models.CharField(blank=True, max_length=16, null=True)),
                ('his_order_num', models.CharField(blank=True, max_length=64, null=True)),
                ('his_order_remark', models.TextField(blank=True, null=True)),
                ('link_cost_order_num', models.CharField(blank=True, max_length=64, null=True)),
                ('charge_one_time_fee', models.DecimalField(blank=True, decimal_places=4, max_digits=14, null=True, verbose_name='一次性费用')),
                ('audit_info', models.TextField(blank=True, null=True)),
                ('charge_tax_type', models.CharField(blank=True, max_length=64, null=True, verbose_name='收入税率类型')),
                ('created', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('updated', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '订单信息表',
                'db_table': 'order_info',
                'ordering': ['-created'],
            },
        ),
        migrations.CreateModel(
            name='OrderLogging',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('total_num', models.CharField(blank=True, max_length=64, null=True, verbose_name='订单号')),
                ('service_status', models.CharField(blank=True, max_length=32, null=True, verbose_name='服务状态')),
                ('bill_status', models.CharField(blank=True, max_length=32, null=True, verbose_name='票据状态')),
                ('creator', models.CharField(max_length=32, verbose_name='创建用户')),
                ('order_id', models.PositiveIntegerField(db_index=True, verbose_name='收入订单ID')),
                ('created', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '订单操作日志表',
                'db_table': 'order_logging',
                'ordering': ['-created'],
            },
        ),
    ]

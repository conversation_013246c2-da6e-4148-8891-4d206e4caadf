from income.contrib.drf.filters import BaseFilter
from income.contrib.drf.utils import get_value


class ContractNumberFilter(BaseFilter):
    """客户编码过滤器

    search_param: 搜索值
    """

    search_param = "contract_num"
    description = "合同编号搜索"

    def filter_queryset(self, request, queryset, view):
        contract_num = get_value(request, self.search_param)
        if contract_num:
            queryset = queryset.filter(contract_num=contract_num)
        return queryset

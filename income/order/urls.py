from django.urls import include
from django.urls import path
from rest_framework.routers import SimpleRouter

from .views import OrderInfoViewSet

router = SimpleRouter(trailing_slash=False)
router.register("orders-info", OrderInfoViewSet, basename="order_info")

# logging_router = SimpleRouter(trailing_slash=False)
# logging_router.register("logging", OrderLoggingViewSet, basename="order_logging")

urlpatterns = [
    path("", include(router.urls)),
    # path("orders/<int:order_id>/", include(logging_router.urls)),
]

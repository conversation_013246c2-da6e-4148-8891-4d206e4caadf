from functools import partial


def register(handle_map: dict, *keys):
    """
    将被装饰对象动态注册到 handler_map 字典中.

    >>> handle_map = {}
    ... @register(handle_map, 1, 2, 3)
    ... def func():
    ...    pass

    >>> handle_map
        {
            1: func
            2: func
            3: func
         }
    """

    def inner(obj):
        for key in keys:
            handle_map[key] = obj
        return obj

    return inner


def partial_register(handle_map):
    return partial(register, handle_map)

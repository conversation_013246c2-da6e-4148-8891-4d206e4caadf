from django_redis import get_redis_connection

from income.users.models import Menu
from income.users.models import RoleMenu


def create_role_permission(role_ids: list, user_id: int):
    """创建角色对应菜单的权限,将该角色拥有的菜单唯一标识以及是否可操作的权限存入redis

    :param role_ids: 用户对应的角色ID
    :param user_id: 用户ID
    :return:
    """
    redis = get_redis_connection()

    queryset = RoleMenu.objects.filter(role_id__in=role_ids).values(
        "menu_id",
        "operation",
    )

    # 处理重复的{menu_id: operation}项
    result_dict = {}
    for item in queryset:
        menu_id = item["menu_id"]
        # 如果 menu_id 不在结果中,者当前项的operation为True且已有项的operation不是 True
        if menu_id not in result_dict or (
            item["operation"] is True and result_dict[menu_id]["operation"] is not True
        ):
            result_dict[menu_id] = item

    merge_result = result_dict.values()

    menu_mapping = dict(
        Menu.objects.filter(
            id__in=result_dict.keys(),
        ).values_list("id", "identify"),
    )
    # 将该角色拥有的菜单ID对应的唯一标识存入redis
    redis.delete(f"identify:user-{user_id}")
    redis.sadd(f"identify:user-{user_id}", *menu_mapping.values())

    # 将所有菜单的对应的操作权限存入redis
    # 获取identify和operation的映射关系
    identify_operation = {
        menu_mapping[item["menu_id"]]: "true" if item["operation"] is True else "false"
        for item in merge_result
        if item["operation"] is not None
    }
    redis.delete(f"operation:user-{user_id}")
    redis.hmset(f"operation:user-{user_id}", mapping=identify_operation)

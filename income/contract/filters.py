from income.contrib.drf.filters import BaseFilter
from income.contrib.drf.utils import get_value


class CustomerNumberFilter(BaseFilter):
    """客户编码过滤器

    search_param: 搜索值
    """

    search_param = "main_customer_num"
    description = "客户信息编码搜索"

    def filter_queryset(self, request, queryset, view):
        main_customer_num = get_value(request, self.search_param)
        if main_customer_num:
            queryset = queryset.filter(main_customer_num=main_customer_num)
        return queryset


class ContractNumberFilter(BaseFilter):
    """合同过滤器

    search_param: 搜索值
    """

    search_param = "contract_num"
    description = "合同编码"

    def filter_queryset(self, request, queryset, view):
        main_customer_num = get_value(request, self.search_param)
        if main_customer_num:
            queryset = queryset.filter(contract_num=main_customer_num)
        return queryset

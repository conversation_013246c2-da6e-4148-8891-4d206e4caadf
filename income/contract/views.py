from drf_spectacular.utils import extend_schema
from drf_spectacular.utils import extend_schema_view
from rest_framework import mixins
from rest_framework.decorators import action
from rest_framework.response import Response

from income import const
from income import message
from income.contrib.drf.filters import SearchFilter
from income.contrib.drf.views import GenericViewSet
from income.contrib.drf.views import UpdateModelMixin
from income.permissions import IsAuthenticated
from income.permissions import RoleMenuPermission

from .filters import CustomerNumberFilter
from .models import ContractInfo
from .serializers import ContractSerializer
from .serializers import ContractSimpleSerializer


@extend_schema_view(
    list=extend_schema(summary="获取合同信息"),
    create=extend_schema(summary="新建合同信息"),
    update=extend_schema(summary="编辑合同信息"),
)
@extend_schema(tags=["customers-contract"])
class ContractInfoViewSet(
    GenericViewSet,
    mixins.ListModelMixin,
    mixins.CreateModelMixin,
    UpdateModelMixin,
):
    """合同信息"""

    NOT_FOUND_MESSAGE = message.CONTRACT_NOT_FOUND

    serializer_class = ContractSerializer
    permission_classes = (IsAuthenticated, RoleMenuPermission)

    search_fields = [
        "contract_title",
        "contract_num",
        "create_user",
        "main_customer_num",
    ]
    search_contains = True
    filter_backends = (SearchFilter, CustomerNumberFilter)

    identify = const.MenuIdentify.CONTRACT_INFO

    def get_queryset(self):
        return ContractInfo.objects.all()

    @extend_schema(
        tags=["customers-contract"],
        responses={200: ContractSimpleSerializer(many=True)},
        summary="仅展示合同信息ID、合同标题、合同编号、客户ID",
    )
    @action(
        methods=["get"],
        detail=False,
        url_path="simple-list",
        filter_backends=[],
        pagination_class=None,
    )
    def simple_list(self, request, *args, **kwargs):
        queryset = self.get_queryset().values(
            "id",
            "contract_num",
            "contract_title",
            "main_customer_num",
        )
        return Response(queryset)

from rest_framework import serializers

from income import message
from income.customer.models import CustomerInfo

from .models import ContractInfo


class ContractSerializer(serializers.ModelSerializer):
    class Meta:
        model = ContractInfo
        exclude = ("updated_at",)
        read_only_fields = ("create_user", "group_approve_state")

    def validate_main_customer_num(self, value):
        """校验客户编码是否存在"""
        if self.instance and self.instance.main_customer_num == value:
            return value

        if not CustomerInfo.objects.filter(customer_num=value).exists():
            raise serializers.ValidationError(message.CUSTOMER_NOT_FOUND)
        return value

    def create(self, validated_data):
        create_user = self.context["request"].user.username
        validated_data["create_user"] = create_user
        return self.Meta.model.objects.create(**validated_data)


class ContractSimpleSerializer(serializers.ModelSerializer):
    customer_id = serializers.IntegerField(read_only=True)

    class Meta:
        model = ContractInfo
        fields = (
            "id",
            "contract_title",
            "contract_num",
            "customer_id",
        )

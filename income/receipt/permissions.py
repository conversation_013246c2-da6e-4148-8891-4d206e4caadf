from rest_framework.permissions import BasePermission

from income import message
from income.contrib.drf.shortcuts import get_object_or_404

from .models import IncomeBankStatement


class BankStatementPermission(BasePermission):
    """客户信息权限"""

    def has_permission(self, request, view):
        get_object_or_404(
            IncomeBankStatement.objects.filter(customer_num__isnull=False),
            pk=view.kwargs["bank_statement_id"],
            error_message=message.BANK_STATEMENT_NOT_FOUND,
        )
        return True

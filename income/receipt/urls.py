from django.urls import include
from django.urls import path
from rest_framework.routers import SimpleRouter

from .views import IncomeBankStatementRecognitionViewSet
from .views import IncomeBankStatementViewSet
from .views import IncomeReceiveStatementTmpViewSet
from .views import IncomeReceiveStatementViewSet
from .views import PrepaidViewSet

# 对账信息
router = SimpleRouter(trailing_slash=False)
router.register(
    "bank-statements",
    IncomeBankStatementViewSet,
    basename="bank_statement",
)
# 已认款的信息
receive_statement_router = SimpleRouter(trailing_slash=False)
receive_statement_router.register(
    "receive-statements",
    IncomeReceiveStatementViewSet,
    basename="receive_statement",
)
# 待认款的权责信息
recognition_router = SimpleRouter(trailing_slash=False)
recognition_router.register(
    "recognition-details",
    IncomeBankStatementRecognitionViewSet,
    basename="recognition_detail",
)
# 未确认认款
receive_statement_tmp_router = SimpleRouter(trailing_slash=False)
receive_statement_tmp_router.register(
    "receive-statement-tmp",
    IncomeReceiveStatementTmpViewSet,
    basename="receive_statement_tmp",
)
# 预付费认款
prepaid_router = SimpleRouter(trailing_slash=False)
prepaid_router.register("prepaid", PrepaidViewSet, basename="prepaid")

urlpatterns = [
    path("", include(router.urls)),
    path(
        "bank-statements/<int:bank_statement_id>/",
        include(receive_statement_router.urls),
    ),
    path(
        "bank-statements/<int:bank_statement_id>/",
        include(recognition_router.urls),
    ),
    path(
        "bank-statements/<int:bank_statement_id>/",
        include(receive_statement_tmp_router.urls),
    ),
    path("bank-statements/<int:bank_statement_id>/", include(prepaid_router.urls)),
]

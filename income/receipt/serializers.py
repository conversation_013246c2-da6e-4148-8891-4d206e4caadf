from django.db.models import F
from rest_framework import serializers

from income import const
from income import message
from income.charge.models import IncomeChargeDetail
from income.order.models import IncomeOrderInfo

from .models import IncomeBankStatement
from .models import IncomeReceiveStatement
from .models import IncomeReceiveStatementTmp
from .utils import calc_unconfirmed_amount


class IncomeBankStatementSerializer(serializers.ModelSerializer):
    """对账信息表序列化器"""

    receipt_status = serializers.ChoiceField(
        help_text="流水状态",
        choices=const.ReceiptStatus.choices,
        read_only=True,
    )

    class Meta:
        model = IncomeBankStatement
        exclude = ("updated_at", "state")
        read_only_fields = ("created_at",)

    def update(self, instance, validated_data):
        for k, v in validated_data.items():
            setattr(instance, k, v)
        instance.save(update_fields=validated_data.keys())
        return instance


class IncomeReceiveStatementSerializer(serializers.ModelSerializer):
    """收入确认表序列化器"""

    class Meta:
        model = IncomeReceiveStatement
        exclude = ("updated_at",)
        read_only_fields = ("created_at",)


class IncomeBankStatementRecognitionSerializer(serializers.ModelSerializer):
    """对账信息表认款序列化器"""

    customer_name = serializers.CharField(read_only=True, help_text="客户名称")
    adjust_month = serializers.IntegerField(read_only=True, help_text="调整账期")
    confirming_amount = serializers.IntegerField(
        read_only=True,
        help_text="确认中的金额",
    )

    class Meta:
        model = IncomeChargeDetail
        fields = (
            "id",
            "order_no",
            "sub_order_no",
            "account_seq",
            "pay_type",
            "charge_month",
            "fee_amount",
            "unpay_amount",
            "pay_amount",
            "income_type",
            "tax_type",
            "customer_name",
            "adjust_month",
            "currency_type",
            "confirming_amount",
        )


class ReceiveStatementTmpCreateSerializer(serializers.ModelSerializer):
    """收入确认表临时表序列化器"""

    class Meta:
        model = IncomeReceiveStatementTmp
        exclude = (
            "updated_at",
            "bank_statement_id",
            "receive_statement_id",
            "group_approve_state",
            "remark",
            "receive_type",
        )
        read_only_fields = ("created_at",)

    def validate_amount(self, value):
        if value <= 0:
            raise serializers.ValidationError(message.AMOUNT_MUST_BE_POSITIVE)
        return value


class RecognitionSerializer(serializers.Serializer):
    """后付费认款"""

    receive_statement = serializers.ListSerializer(
        child=ReceiveStatementTmpCreateSerializer(),
        help_text="认款的权责信息list",
    )

    def validate(self, attrs):
        # 校验认款的总金额是否大于当前银行流水的总金额
        total_amount = sum(item["amount"] for item in attrs["receive_statement"])
        bank_statement_id = self.context["view"].kwargs["bank_statement_id"]
        # 计算当前银行流水的剩余金额
        unconfirmed_amount = calc_unconfirmed_amount(bank_statement_id)
        if total_amount > unconfirmed_amount:
            raise serializers.ValidationError(
                {"receive_statement": message.AMOUNT_EXCEEDS_BANK_STATEMENT},
            )
        return attrs

    def create(self, validated_data):
        # 银行流水ID
        bank_statement_id = self.context["view"].kwargs["bank_statement_id"]
        # 在临时表中批量创建认款信息
        IncomeReceiveStatementTmp.objects.bulk_create(
            [
                IncomeReceiveStatementTmp(
                    amount=item["amount"],
                    sub_order_no=item["sub_order_no"],
                    receive_type=const.ReceiveType.WRITE_OFF,
                    income_charge_detail_id=item["income_charge_detail_id"],
                    bank_statement_id=bank_statement_id,
                )
                for item in validated_data["receive_statement"]
            ],
            batch_size=500,
        )
        # 获取当前批次的认款总金额
        confirming_amount = sum(
            item["amount"] for item in validated_data["receive_statement"]
        )
        # 将confirming_amount的值累加到银行流水的confirming_amount字段上
        IncomeBankStatement.objects.filter(pk=bank_statement_id).update(
            confirming_amount=F("confirming_amount") + confirming_amount,
        )
        return {"message": "后付费认款成功"}

    def to_representation(self, data):
        return data


class ReceiveStatementTmpSerializer(serializers.ModelSerializer):
    """收入确认表临时表更新序列化器"""

    class Meta:
        model = IncomeReceiveStatementTmp
        exclude = (
            "updated_at",
            "remark",
            "receive_statement_id",
            "income_charge_detail_id",
            "bank_statement_id",
        )


class ReceiveStatementTmpApproveSerializer(serializers.Serializer):
    """收入确认表临时表提交审批序列化器"""

    tmp_ids = serializers.ListField(
        child=serializers.IntegerField(),
        help_text="收入确认表临时表id列表",
    )

    def validate_tmp_ids(self, value):
        bank_statement_id = self.context["view"].kwargs["bank_statement_id"]
        exists_ids = IncomeReceiveStatementTmp.objects.filter(
            bank_statement_id=bank_statement_id,
            group_approve_state=const.GroupApproveState.INIT,
        ).values_list("id", flat=True)
        if not set(value).issubset(set(exists_ids)):
            raise serializers.ValidationError(message.TMP_IDS_NOT_FOUND)
        return value


class PrepaidSerializer(serializers.ModelSerializer):
    """预付费认款序列化器"""

    class Meta:
        model = IncomeOrderInfo
        fields = (
            "order_num",
            "total_num",
            "income_type",
            "account_seq",
            "bill_status",
            "pay_type",
        )


class PerpaidReceiveStatementTmpSerializer(serializers.ModelSerializer):
    """预付费认款的收入确认表临时表序列化器"""

    class Meta:
        model = IncomeReceiveStatementTmp
        exclude = (
            "updated_at",
            "remark",
            "income_charge_detail_id",
            "bank_statement_id",
            "receive_statement_id",
            "group_approve_state",
            "receive_type",
        )

    def validate_amount(self, value):
        if value <= 0:
            raise serializers.ValidationError(message.AMOUNT_MUST_BE_POSITIVE)
        return value


class PrepaidRecognitionSerializer(serializers.Serializer):
    """预付费认款"""

    prepaid = serializers.ListSerializer(
        child=PerpaidReceiveStatementTmpSerializer(),
        help_text="预付费认款的list",
    )

    def validate(self, attrs):
        # 校验认款的总金额是否大于当前银行流水的总金额
        total_amount = sum(item["amount"] for item in attrs["prepaid"])
        bank_statement_id = self.context["view"].kwargs["bank_statement_id"]
        # 计算当前银行流水的剩余金额
        unconfirmed_amount = calc_unconfirmed_amount(bank_statement_id)
        if total_amount > unconfirmed_amount:
            raise serializers.ValidationError(
                {"prepaid": message.AMOUNT_EXCEEDS_BANK_STATEMENT},
            )
        return attrs

    def create(self, validated_data):
        # 银行流水ID
        bank_statement_id = self.context["view"].kwargs["bank_statement_id"]
        # 在临时表中批量创建认款信息
        IncomeReceiveStatementTmp.objects.bulk_create(
            [
                IncomeReceiveStatementTmp(
                    amount=item["amount"],
                    sub_order_no=item["sub_order_no"],
                    receive_type=const.ReceiveType.PAYMENT_RECEIVED,
                    bank_statement_id=bank_statement_id,
                )
                for item in validated_data["prepaid"]
            ],
            batch_size=500,
        )
        # 获取当前批次的认款总金额
        confirming_amount = sum(item["amount"] for item in validated_data["prepaid"])
        # 将confirming_amount的值累加到银行流水的confirming_amount字段上
        IncomeBankStatement.objects.filter(pk=bank_statement_id).update(
            confirming_amount=F("confirming_amount") + confirming_amount,
        )
        return {"message": "预付费认款成功"}

    def to_representation(self, data):
        return data

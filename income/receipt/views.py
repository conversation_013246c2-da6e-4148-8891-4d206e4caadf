from django.db.models import Case
from django.db.models import F
from django.db.models import Sum
from django.db.models import Value
from django.db.models import When
from drf_spectacular.utils import extend_schema
from drf_spectacular.utils import extend_schema_view
from rest_framework import mixins
from rest_framework.decorators import action
from rest_framework.response import Response

from income import const
from income.charge.filters import IncomeChargeDetailFieldsFilter
from income.charge.models import IncomeChargeDetail
from income.contrib.drf.filters import SearchAndFilter
from income.contrib.drf.views import GenericViewSet
from income.order.models import IncomeOrderInfo
from income.permissions import IsAuthenticated
from income.permissions import RoleMenuPermission

from .filters import ReceiptStatusFilter
from .filters import UnConfirmedStatusFilter
from .models import IncomeBankStatement
from .models import IncomeReceiveStatement
from .models import IncomeReceiveStatementTmp
from .permissions import BankStatementPermission
from .serializers import IncomeBankStatementRecognitionSerializer
from .serializers import IncomeBankStatementSerializer
from .serializers import IncomeReceiveStatementSerializer
from .serializers import PrepaidRecognitionSerializer
from .serializers import PrepaidSerializer
from .serializers import ReceiveStatementTmpApproveSerializer
from .serializers import ReceiveStatementTmpSerializer
from .serializers import RecognitionSerializer
from .utils import add_extra_field


@extend_schema_view(
    list=extend_schema(summary="获取对账信息"),
    update=extend_schema(summary="更新对账信息"),
    retrieve=extend_schema(summary="获取对账信息详情"),
)
@extend_schema(tags=["bank-statement"])
class IncomeBankStatementViewSet(
    GenericViewSet,
    mixins.RetrieveModelMixin,
):
    serializer_class = IncomeBankStatementSerializer
    permission_classes = [IsAuthenticated, RoleMenuPermission]
    filter_backends = [SearchAndFilter, ReceiptStatusFilter]
    search_fields = ["statement_no", "payment_name"]
    search_contains = True

    receipt_status_choices = const.ReceiptStatus

    identify = const.MenuIdentify.INCOME_RECEIPT

    def get_queryset(self):
        return IncomeBankStatement.objects.all()

    def update(self, request, *args, **kwargs):
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=True)
        serializer.is_valid(raise_exception=True)
        self.perform_update(serializer)
        return Response(serializer.data)

    def perform_update(self, serializer):
        serializer.save()

    def list(self, request, *args, **kwargs):
        fields = (
            "id",
            "amount",
            "currency_type",
            "statement_no",
            "statement_date",
            "payment_name",
            "payment_bank_no",
            "payment_bank_name",
            "receive_bank_name",
            "receive_bank_no",
            "confirming_amount",
            "confirmed_amount",
            "customer_num",
            "customer_name",
            "description",
            "created_at",
        )
        queryset = (
            self.filter_queryset(
                self.get_queryset(),
            )
            .values(*fields)
            .annotate(
                receipt_status=Case(
                    When(
                        amount__gt=F("confirmed_amount"),
                        confirmed_amount=0,
                        then=Value(const.ReceiptStatus.PENDING_CLAIM),
                    ),
                    When(
                        confirming_amount__gt=0,
                        then=Value(const.ReceiptStatus.PENDING_CONFIRMATION),
                    ),
                    When(
                        amount=F("confirmed_amount"),
                        then=Value(const.ReceiptStatus.CONFIRMED),
                    ),
                ),
            )
        )
        page = self.paginate_queryset(queryset)
        if page is not None:
            queryset = add_extra_field(page)
            return self.get_paginated_response(queryset)
        queryset = add_extra_field(queryset)
        return Response(queryset)


@extend_schema_view(
    list=extend_schema(summary="获取已认款的信息"),
)
@extend_schema(tags=["receive-statement"])
class IncomeReceiveStatementViewSet(
    GenericViewSet,
    mixins.ListModelMixin,
):
    """已认款的信息"""

    serializer_class = IncomeReceiveStatementSerializer
    permission_classes = [IsAuthenticated, RoleMenuPermission, BankStatementPermission]

    identify = const.MenuIdentify.INCOME_RECEIPT

    def get_queryset(self):
        return IncomeReceiveStatement.objects.filter(
            bank_statement_id=self.kwargs["bank_statement_id"],
        )


@extend_schema_view(
    list=extend_schema(summary="获取待认款的权责信息"),
    recognition=extend_schema(summary="认款"),
)
@extend_schema(tags=["recognition-detail"])
class IncomeBankStatementRecognitionViewSet(
    GenericViewSet,
    mixins.ListModelMixin,
):
    """待认款的权责信息"""

    serializer_class = IncomeBankStatementRecognitionSerializer
    serializers = {
        "recognition": RecognitionSerializer,
    }
    permission_classes = [IsAuthenticated, RoleMenuPermission, BankStatementPermission]
    filter_backends = [IncomeChargeDetailFieldsFilter]
    search_fields = ["order_no", "account_seq", "charge_month", "adjust_month"]
    search_contains = True
    table_mapping = {
        "order_no": "a",
        "account_seq": "a",
        "charge_month": "a",
        "adjust_month": "iai",
    }

    identify = const.MenuIdentify.INCOME_RECEIPT

    def get_queryset(self):
        """
        使用Raw SQL获取完整数据, 减少内存使用
        """
        customer_num = IncomeBankStatement.objects.values_list(
            "customer_num",
            flat=True,
        ).get(pk=self.kwargs["bank_statement_id"])
        return self._get_raw_queryset(customer_num=customer_num)

    def get_serializer_class(self):
        return self.serializers.get(self.action, self.serializer_class)

    def _get_raw_queryset(self, customer_num: str):
        """使用Raw SQL获取完整数据"""
        sql = f"""
        SELECT
            a.id,
            ci.`customer_name`,
            a.`order_no`,
            a.`sub_order_no`,
            a.`account_seq`,
            a.`pay_type`,
            a.`charge_month`,
            iai.`adjust_month`,
            a.`fee_amount`,
            a.`unpay_amount`,
            a.`pay_amount`,
            a.`income_type`,
            a.`tax_type`,
            a.`currency_type`,
            COALESCE(tmp.`confirming_amount`, 0) AS confirming_amount
        FROM
            `income_charge_detail` a
            LEFT JOIN customer_info ci ON a.`customer_num` = ci.`customer_num`
            LEFT JOIN `income_adjust_detail` iai ON iai.id = a.`income_adjust_id`
            LEFT JOIN (
                SELECT income_charge_detail_id, SUM(amount) AS confirming_amount
                FROM income_receive_statement_tmp
                WHERE group_approve_state in (0, 1, 3)
                AND bank_statement_id = {self.kwargs["bank_statement_id"]}
                GROUP BY income_charge_detail_id
            ) tmp ON a.id = tmp.income_charge_detail_id
        WHERE
            a.`customer_num` = '{customer_num}'
            AND a.`unpay_amount` != 0
            AND (tmp.confirming_amount IS NULL OR tmp.confirming_amount < a.unpay_amount)
        ORDER BY a.`created_at` DESC
        """  # noqa:E501
        return IncomeChargeDetail.objects.raw(sql)

    @action(
        methods=["post"],
        detail=False,
        url_path="recognition",
    )
    def recognition(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response(serializer.data)


@extend_schema_view(
    list=extend_schema(summary="获取认款信息"),
    approve=extend_schema(summary="提交审批认款信息"),
    revoked=extend_schema(summary="撤销认款信息"),
)
@extend_schema(tags=["receive-statement-tmp"])
class IncomeReceiveStatementTmpViewSet(
    GenericViewSet,
    mixins.ListModelMixin,
):
    serializer_class = ReceiveStatementTmpSerializer
    serializers = {
        "approve": ReceiveStatementTmpApproveSerializer,
        "revoked": ReceiveStatementTmpApproveSerializer,
    }
    filter_backends = [UnConfirmedStatusFilter]
    unconfirmed_approve_state_choices = const.UnConfirmedApproveState
    permission_classes = [IsAuthenticated, RoleMenuPermission, BankStatementPermission]

    identify = const.MenuIdentify.INCOME_RECEIPT

    def get_queryset(self):
        return IncomeReceiveStatementTmp.objects.filter(
            bank_statement_id=self.kwargs["bank_statement_id"],
        )

    def get_serializer_class(self):
        return self.serializers.get(self.action, self.serializer_class)

    @action(
        methods=["post"],
        detail=False,
        url_path="approve",
    )
    def approve(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        tmp_ids = serializer.validated_data["tmp_ids"]
        IncomeReceiveStatementTmp.objects.filter(id__in=tmp_ids).update(
            group_approve_state=const.GroupApproveState.PENDING,
        )
        return Response({"message": "提交审批成功"})

    @action(
        methods=["post"],
        detail=False,
        url_path="revoked",
    )
    def revoked(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        tmp_ids = serializer.validated_data["tmp_ids"]
        queryset = IncomeReceiveStatementTmp.objects.filter(id__in=tmp_ids)
        # 获取当前批次的认款总金额
        confirming_amount = queryset.aggregate(Sum("amount"))["amount__sum"]
        # 将confirming_amount的值从银行流水的confirming_amount字段上减去
        IncomeBankStatement.objects.filter(
            pk=self.kwargs["bank_statement_id"],
        ).update(
            confirming_amount=F("confirming_amount") - confirming_amount,
        )
        # 直接删除暂存的认款信息
        queryset.delete()
        return Response({"message": "撤销认款信息成功"})


@extend_schema_view(
    list=extend_schema(summary="获取预付费认款信息"),
    recognition=extend_schema(summary="预付费认款"),
)
@extend_schema(tags=["prepaid"])
class PrepaidViewSet(
    GenericViewSet,
    mixins.ListModelMixin,
):
    serializer_class = PrepaidSerializer
    serializers = {
        "recognition": PrepaidRecognitionSerializer,
    }
    permission_classes = [IsAuthenticated, RoleMenuPermission, BankStatementPermission]
    filter_backends = [SearchAndFilter]
    search_fields = ["order_num"]
    search_contains = True

    identify = const.MenuIdentify.INCOME_RECEIPT

    def get_queryset(self):
        customer_num = IncomeBankStatement.objects.values_list(
            "customer_num",
            flat=True,
        ).get(pk=self.kwargs["bank_statement_id"])
        return IncomeOrderInfo.objects.filter(
            bill_status__in=[
                const.OrderBillStatus.IN_PROGRESS,
                const.OrderBillStatus.DECOM_BILLING_CONFIRMED,
                const.OrderBillStatus.CHG_DECOM_BILLING_CONFIRMED,
            ],
            customer_num=customer_num,
            pay_type=const.PayType.PREPAID,
        )

    def get_serializer_class(self):
        return self.serializers.get(self.action, self.serializer_class)

    @action(
        methods=["post"],
        detail=False,
        url_path="recognition",
    )
    def recognition(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response(serializer.data)

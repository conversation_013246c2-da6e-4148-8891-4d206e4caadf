from django.conf import settings


class DatabaseAppsRouter:
    DATABASE_MAPPING = settings.DATABASE_APPS_MAPPING  # 在setting中定义的路由表

    def db_for_read(self, model, **hints):
        if model._meta.app_label in self.DATABASE_MAPPING:  # noqa
            return self.DATABASE_MAPPING[model._meta.app_label]  # noqa
        return None

    def db_for_write(self, model, **hints):
        if model._meta.app_label in self.DATABASE_MAPPING:  # noqa
            return self.DATABASE_MAPPING[model._meta.app_label]  # noqa
        return None

from django.conf import settings
from django.urls import include
from django.urls import path

urlpatterns = [
    path("", include("income.users.urls")),
    path("", include("income.order.urls")),
    path("", include("income.contract.urls")),
    path("", include("income.customer.urls")),
    path("", include("income.public.urls")),
    path("", include("income.fee.urls")),
    path("", include("income.charge.urls")),
    path("", include("income.receipt.urls")),
    path("", include("income.invoice.urls")),
]

if settings.ENVIRONMENT in ["test"]:
    from drf_spectacular.views import SpectacularAPIView
    from drf_spectacular.views import SpectacularRedocView

    from income.contrib.drf.swagger import SpectacularSwaggerAutoAuthView

    urlpatterns += [
        # Swagger
        path(
            "local/",
            include("rest_framework.urls", namespace="rest_framework"),
        ),  # 登录
        path("schema/", SpectacularAPIView.as_view(), name="api-schema"),
        path(
            "docs/",
            SpectacularSwaggerAutoAuthView.as_view(url_name="api-schema"),
            name="api-docs",
        ),
        path(
            "redoc/", SpectacularRedocView.as_view(url_name="api-schema"), name="redoc",
        ),
    ]

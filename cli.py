import os
import argparse
import watchgod
import importlib

parser = argparse.ArgumentParser(description='Process some integers.')
parser.add_argument('path', help=u'方法指向的路径， 如 foo.bar  即 foo文件的bar函数')
parser.add_argument('--reload', help='是否要重载', type=bool, default=False)
parser.add_argument('args', nargs='*', help=u'函数的参数', default='')
parser = parser.parse_args()
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.local')


def import_module(dotted_path):
    dotted_path = dotted_path.replace('/', '.')
    module_parts = dotted_path.split('.')
    if len(module_parts) < 2:
        module_parts.append('main')  # 默认调用main函数
    if len(module_parts) == 2:
        module_parts.insert(0, 'utility')  # 默认调用utility/下的文件
    module_path = ".".join(module_parts[:-1])
    module = importlib.import_module(module_path)
    return getattr(module, module_parts[-1])


def django_setup():
    try:
        from django import setup
        setup()
    except ImportError:
        pass


def reload_run(path, *args):
    django_setup()
    import_module(path)(*args)


def main():
    """
    python cli.py rabbit  即调用utility/rabbit.py 下的main函数
    python cli.py rabbit.listen  即调用utility/rabbit.py 下的listen函数
    """
    path, args = parser.path, parser.args or []
    if parser.reload:
        watchgod.run_process("./", reload_run, args=(path, *args))
    else:
        django_setup()
        import_module(path)(*args)


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        pass
